import { Yuga } from '../types';

export interface AudioTrack {
  id: string;
  name: string;
  path: string;
  loop: boolean;
  volume: number;
  category: 'meditation' | 'yuga' | 'ambient';
}

class AudioService {
  private audioElements: Map<string, HTMLAudioElement> = new Map();
  private currentTrack: string | null = null;
  private isEnabled: boolean = true;
  private masterVolume: number = 0.7;

  // Audio track definitions
  private tracks: AudioTrack[] = [
    {
      id: 'meditation_relaxing',
      name: 'Relaxing Meditation Music',
      path: '/2%20minutes%20of%20relaxing%20music,2%20minute%20meditation%20music,2%20minutes%20meditation,music%202%20minute.mp3',
      loop: true,
      volume: 0.5,
      category: 'meditation'
    },
    {
      id: 'meditation_om',
      name: 'Om Chanting',
      path: '/One%20minute%20Om%20Chanting.mp3',
      loop: true,
      volume: 0.6,
      category: 'meditation'
    },
    {
      id: 'yuga_om',
      name: 'Sacred Om for Yugas',
      path: '/One%20minute%20Om%20Chanting.mp3',
      loop: true,
      volume: 0.4,
      category: 'yuga'
    },
    {
      id: 'yuga_kali',
      name: 'Kali Yuga Theme',
      path: '/Hans%20Zimmer%20&%20James%20Newton%20Howard%20-%20Why%20So%20Serious_%20(Official%20Audio).mp3',
      loop: true,
      volume: 0.3,
      category: 'yuga'
    },
    {
      id: 'relaxing_ambient',
      name: 'Relaxing Ambient',
      path: '/relaxing-music-2min.mp3',
      loop: true,
      volume: 0.3,
      category: 'ambient'
    }
  ];

  constructor() {
    this.initializeAudio();
  }

  private initializeAudio() {
    // Initialize all audio elements
    this.tracks.forEach(track => {
      const audio = new Audio();
      audio.src = track.path;
      audio.loop = track.loop;
      audio.volume = track.volume * this.masterVolume;
      audio.preload = 'auto';
      
      // Handle audio loading errors
      audio.addEventListener('error', (e) => {
        console.warn(`Failed to load audio track: ${track.name}`, e);
      });

      audio.addEventListener('canplaythrough', () => {
        console.log(`Audio track loaded: ${track.name}`);
      });

      this.audioElements.set(track.id, audio);
    });
  }

  // Enable/disable audio globally
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
    if (!enabled) {
      this.stopAll();
    }
  }

  // Set master volume (0-1)
  setMasterVolume(volume: number) {
    this.masterVolume = Math.max(0, Math.min(1, volume));
    this.audioElements.forEach((audio, trackId) => {
      const track = this.tracks.find(t => t.id === trackId);
      if (track) {
        audio.volume = track.volume * this.masterVolume;
      }
    });
  }

  // Play a specific track
  async playTrack(trackId: string): Promise<boolean> {
    if (!this.isEnabled) return false;

    const audio = this.audioElements.get(trackId);
    if (!audio) {
      console.warn(`Audio track not found: ${trackId}`);
      return false;
    }

    try {
      // Stop current track if different
      if (this.currentTrack && this.currentTrack !== trackId) {
        await this.stopTrack(this.currentTrack);
      }

      audio.currentTime = 0;
      await audio.play();
      this.currentTrack = trackId;
      console.log(`Playing audio track: ${trackId}`);
      return true;
    } catch (error) {
      console.error(`Failed to play audio track: ${trackId}`, error);
      return false;
    }
  }

  // Stop a specific track
  async stopTrack(trackId: string): Promise<void> {
    const audio = this.audioElements.get(trackId);
    if (audio) {
      audio.pause();
      audio.currentTime = 0;
      if (this.currentTrack === trackId) {
        this.currentTrack = null;
      }
    }
  }

  // Stop all audio
  stopAll(): void {
    this.audioElements.forEach(audio => {
      audio.pause();
      audio.currentTime = 0;
    });
    this.currentTrack = null;
  }

  // Pause current track
  pauseCurrent(): void {
    if (this.currentTrack) {
      const audio = this.audioElements.get(this.currentTrack);
      if (audio) {
        audio.pause();
      }
    }
  }

  // Resume current track
  async resumeCurrent(): Promise<boolean> {
    if (this.currentTrack && this.isEnabled) {
      const audio = this.audioElements.get(this.currentTrack);
      if (audio) {
        try {
          await audio.play();
          return true;
        } catch (error) {
          console.error('Failed to resume audio', error);
        }
      }
    }
    return false;
  }

  // Get appropriate music for meditation
  getMeditationTrack(): string {
    return 'meditation_relaxing';
  }

  // Get appropriate music for yuga
  getYugaTrack(yuga: Yuga): string {
    if (yuga === 'kali') {
      return 'yuga_kali';
    }
    return 'yuga_om'; // For satya, treta, dvapara
  }

  // Check if audio is currently playing
  isPlaying(): boolean {
    if (!this.currentTrack) return false;
    const audio = this.audioElements.get(this.currentTrack);
    return audio ? !audio.paused : false;
  }

  // Get current track info
  getCurrentTrack(): AudioTrack | null {
    if (!this.currentTrack) return null;
    return this.tracks.find(t => t.id === this.currentTrack) || null;
  }

  // Get all available tracks by category
  getTracksByCategory(category: AudioTrack['category']): AudioTrack[] {
    return this.tracks.filter(t => t.category === category);
  }

  // Fade out current track and fade in new track
  async crossfade(newTrackId: string, duration: number = 2000): Promise<boolean> {
    if (!this.isEnabled) return false;

    const newAudio = this.audioElements.get(newTrackId);
    if (!newAudio) return false;

    const oldAudio = this.currentTrack ? this.audioElements.get(this.currentTrack) : null;

    // Start new track at volume 0
    const newTrack = this.tracks.find(t => t.id === newTrackId);
    if (!newTrack) return false;

    newAudio.volume = 0;

    try {
      await newAudio.play();
      this.currentTrack = newTrackId;

      // Crossfade
      const steps = 50;
      const stepDuration = duration / steps;
      const oldVolume = oldAudio ? oldAudio.volume : 0;
      const newVolume = newTrack.volume * this.masterVolume;

      for (let i = 0; i <= steps; i++) {
        const progress = i / steps;

        if (oldAudio) {
          oldAudio.volume = oldVolume * (1 - progress);
        }
        newAudio.volume = newVolume * progress;

        await new Promise(resolve => setTimeout(resolve, stepDuration));
      }

      // Stop old audio
      if (oldAudio) {
        oldAudio.pause();
        oldAudio.currentTime = 0;
      }

      return true;
    } catch (error) {
      console.error(`Failed to crossfade to track: ${newTrackId}`, error);
      return false;
    }
  }

  // Play music based on current yuga with smooth transition
  async playYugaMusic(yuga: Yuga): Promise<boolean> {
    const trackId = this.getYugaTrack(yuga);

    // If same track is already playing, do nothing
    if (this.currentTrack === trackId && this.isPlaying()) {
      return true;
    }

    // Use crossfade for smooth transition
    return await this.crossfade(trackId, 3000);
  }

  // Play meditation music
  async playMeditationMusic(): Promise<boolean> {
    const trackId = this.getMeditationTrack();
    return await this.playTrack(trackId);
  }

  // Initialize audio with user interaction (required for autoplay policies)
  async initializeWithUserInteraction(): Promise<void> {
    // Try to play and immediately pause each track to initialize
    for (const [trackId, audio] of this.audioElements) {
      try {
        await audio.play();
        audio.pause();
        audio.currentTime = 0;
      } catch (error) {
        console.log(`Could not initialize audio track: ${trackId}`);
      }
    }
  }
}

// Create singleton instance
export const audioService = new AudioService();
