{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Master1\\\\karmaverse\\\\src\\\\components\\\\World\\\\WorldExplorer.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useGameStore } from '../../store/gameStore';\nimport { Sun, Mountain } from 'lucide-react';\nimport TempleList from './TempleList';\nimport YugaDetails from './YugaDetails';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorldExplorer = () => {\n  _s();\n  const {\n    worldState,\n    avatar,\n    transitionYuga,\n    updateKarma,\n    updateVirtues,\n    addNotification\n  } = useGameStore();\n  const [selectedLocation, setSelectedLocation] = useState(null);\n  const [isTransitioning, setIsTransitioning] = useState(false);\n  const [showTemples, setShowTemples] = useState(false);\n  const [showYugaDetails, setShowYugaDetails] = useState(false);\n  const [yugaAnimation, setYugaAnimation] = useState(null);\n\n  // Audio refs for Yuga music\n  const omChantingRef = useRef(null);\n  const whySoSeriousRef = useRef(null);\n  const yugaDescriptions = {\n    satya: {\n      name: 'Satya Yuga',\n      title: 'The Golden Age of Truth',\n      description: 'An age of perfect virtue, where dharma reigns supreme and all beings live in harmony with divine law.',\n      characteristics: ['Perfect righteousness', 'No suffering or disease', 'Natural abundance', 'Divine communion'],\n      duration: '1,728,000 years',\n      color: 'from-yellow-200 to-green-200',\n      textColor: 'text-green-800',\n      bgPattern: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23228B22' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n      icon: '🌅',\n      cardStyle: 'border-green-300 shadow-green-200/50'\n    },\n    treta: {\n      name: 'Treta Yuga',\n      title: 'The Silver Age of Sacrifice',\n      description: 'Virtue begins to decline, but dharma is still strong. Great sacrifices and rituals maintain cosmic order.',\n      characteristics: ['Three-quarters virtue', 'Introduction of rituals', 'Emergence of kingdoms', 'Heroic deeds'],\n      duration: '1,296,000 years',\n      color: 'from-yellow-100 to-orange-200',\n      textColor: 'text-orange-800',\n      bgPattern: \"url(\\\"data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ed7611' fill-opacity='0.1'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n      icon: '🌞',\n      cardStyle: 'border-orange-300 shadow-orange-200/50'\n    },\n    dvapara: {\n      name: 'Dvapara Yuga',\n      title: 'The Bronze Age of Duality',\n      description: 'Virtue and vice are equally balanced. Knowledge becomes divided and conflicts arise between good and evil.',\n      characteristics: ['Half virtue remains', 'Division of knowledge', 'Rise of conflicts', 'Material progress'],\n      duration: '864,000 years',\n      color: 'from-blue-100 to-purple-200',\n      textColor: 'text-purple-800',\n      bgPattern: \"url(\\\"data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Cpath d='M0 0h20L0 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n      icon: '🌆',\n      cardStyle: 'border-purple-300 shadow-purple-200/50'\n    },\n    kali: {\n      name: 'Kali Yuga',\n      title: 'The Iron Age of Darkness',\n      description: 'The age of darkness and materialism. Virtue is at its lowest, but spiritual liberation is most accessible.',\n      characteristics: ['Quarter virtue remains', 'Widespread ignorance', 'Material obsession', 'Easy liberation'],\n      duration: '432,000 years',\n      color: 'from-gray-200 to-gray-400',\n      textColor: 'text-gray-800',\n      bgPattern: \"url(\\\"data:image/svg+xml,%3Csvg width='6' height='6' viewBox='0 0 6 6' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cpath d='M5 0h1L0 5v1H5z'/%3E%3C/g%3E%3C/svg%3E\\\")\",\n      icon: '🌃',\n      cardStyle: 'border-gray-300 shadow-gray-200/50'\n    }\n  };\n  const locations = [{\n    id: 'temple',\n    name: 'Sacred Temples',\n    icon: '🏛️',\n    description: 'Visit ancient Hindu temples across India',\n    benefits: {\n      karma: 10,\n      sattva: 5\n    },\n    available: true,\n    special: 'temples'\n  }, {\n    id: 'forest',\n    name: 'Enchanted Forest',\n    icon: '🌲',\n    description: 'Meditate among ancient trees and connect with nature',\n    benefits: {\n      karma: 8,\n      sattva: 8\n    },\n    available: worldState.environment.spirituality > 30\n  }, {\n    id: 'mountain',\n    name: 'Sacred Mountain',\n    icon: '⛰️',\n    description: 'Climb to spiritual heights and gain wisdom',\n    benefits: {\n      karma: 15,\n      sattva: 10\n    },\n    available: worldState.environment.harmony > 50\n  }, {\n    id: 'river',\n    name: 'Holy River',\n    icon: '🌊',\n    description: 'Purify your soul in sacred waters',\n    benefits: {\n      karma: 12,\n      sattva: 7\n    },\n    available: true\n  }, {\n    id: 'village',\n    name: 'Dharmic Village',\n    icon: '🏘️',\n    description: 'Serve the community and practice karma yoga',\n    benefits: {\n      karma: 20,\n      sattva: 3\n    },\n    available: worldState.environment.prosperity > 40\n  }, {\n    id: 'cave',\n    name: 'Meditation Cave',\n    icon: '🕳️',\n    description: 'Deep contemplation in solitude',\n    benefits: {\n      karma: 5,\n      sattva: 15\n    },\n    available: worldState.currentYuga !== 'kali'\n  }];\n  const handleLocationVisit = location => {\n    if (!avatar || !location.available) return;\n    updateKarma({\n      id: `location_${Date.now()}`,\n      action: `Visited ${location.name}`,\n      karmaChange: location.benefits.karma,\n      gunaEffect: {\n        sattva: location.benefits.sattva\n      },\n      timestamp: new Date(),\n      context: `World exploration in ${worldState.currentYuga} Yuga`\n    });\n    addNotification(`Visited ${location.name}: +${location.benefits.karma} karma, +${location.benefits.sattva} sattva`);\n    setSelectedLocation(null);\n  };\n  const handleTempleVisit = temple => {\n    if (!avatar) return;\n    updateKarma({\n      id: `temple_${Date.now()}`,\n      action: `Visited ${temple.name}`,\n      karmaChange: temple.benefits.karma,\n      gunaEffect: {\n        sattva: temple.benefits.sattva\n      },\n      timestamp: new Date(),\n      context: `Temple pilgrimage in ${worldState.currentYuga} Yuga`\n    });\n\n    // Update devotion virtue\n    updateVirtues({\n      devotion: temple.benefits.devotion / 5 // Scale down for balance\n    });\n    addNotification(`Visited ${temple.name}: +${temple.benefits.karma} karma, +${temple.benefits.sattva} sattva, +${temple.benefits.devotion / 5} devotion`);\n    setShowTemples(false);\n  };\n  const handleYugaTransition = newYuga => {\n    if (newYuga === worldState.currentYuga) return;\n    setIsTransitioning(true);\n\n    // Set animation type for the Yuga\n    setYugaAnimation(newYuga);\n\n    // Play the appropriate music\n    if (['satya', 'treta', 'dvapara'].includes(newYuga)) {\n      var _whySoSeriousRef$curr, _omChantingRef$curren;\n      (_whySoSeriousRef$curr = whySoSeriousRef.current) === null || _whySoSeriousRef$curr === void 0 ? void 0 : _whySoSeriousRef$curr.pause();\n      whySoSeriousRef.current && (whySoSeriousRef.current.currentTime = 0);\n      (_omChantingRef$curren = omChantingRef.current) === null || _omChantingRef$curren === void 0 ? void 0 : _omChantingRef$curren.play();\n    } else if (newYuga === 'kali') {\n      var _omChantingRef$curren2, _whySoSeriousRef$curr2;\n      (_omChantingRef$curren2 = omChantingRef.current) === null || _omChantingRef$curren2 === void 0 ? void 0 : _omChantingRef$curren2.pause();\n      omChantingRef.current && (omChantingRef.current.currentTime = 0);\n      (_whySoSeriousRef$curr2 = whySoSeriousRef.current) === null || _whySoSeriousRef$curr2 === void 0 ? void 0 : _whySoSeriousRef$curr2.play();\n    }\n    setTimeout(() => {\n      transitionYuga(newYuga);\n      setIsTransitioning(false);\n    }, 1000);\n  };\n  const getEnvironmentIcon = (aspect, value) => {\n    const icons = {\n      harmony: value > 70 ? '☮️' : value > 40 ? '⚖️' : '⚔️',\n      prosperity: value > 70 ? '💰' : value > 40 ? '🏠' : '🍞',\n      spirituality: value > 70 ? '🕉️' : value > 40 ? '🙏' : '📿',\n      conflict: value > 70 ? '⚔️' : value > 40 ? '🤝' : '☮️'\n    };\n    return icons[aspect] || '❓';\n  };\n  const currentYuga = yugaDescriptions[worldState.currentYuga];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen pt-20 p-6\",\n    style: {\n      backgroundImage: currentYuga.bgPattern,\n      backgroundColor: worldState.currentYuga === 'satya' ? '#f0fff4' : worldState.currentYuga === 'treta' ? '#fffaf0' : worldState.currentYuga === 'dvapara' ? '#f5f0ff' : '#f5f5f5'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"audio\", {\n      ref: omChantingRef,\n      src: \"/One%20minute%20Om%20Chanting.mp3\",\n      preload: \"auto\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"audio\", {\n      ref: whySoSeriousRef,\n      src: \"/Hans%20Zimmer%20&%20James%20Newton%20Howard%20-%20Why%20So%20Serious_%20(Official%20Audio).mp3\",\n      preload: \"auto\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"text-center mb-8\",\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-spiritual font-bold text-saffron-700 mb-2\",\n          children: \"World Explorer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Journey through the cosmic cycles and sacred locations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"lg:col-span-2\",\n          initial: {\n            opacity: 0,\n            scale: 0.95\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `spiritual-card p-8 bg-gradient-to-br ${currentYuga.color} relative overflow-hidden border ${currentYuga.cardStyle} transition-all duration-500`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 opacity-10\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full h-full\",\n                style: {\n                  backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative z-10\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: `text-3xl font-bold ${currentYuga.textColor} mb-2`,\n                    children: currentYuga.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-lg ${currentYuga.textColor} opacity-80`,\n                    children: currentYuga.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl\",\n                  children: currentYuga.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: `${currentYuga.textColor} opacity-90 mb-6 leading-relaxed`,\n                children: currentYuga.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4 mb-6\",\n                children: currentYuga.characteristics.map((char, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"flex items-center space-x-2\",\n                  initial: {\n                    opacity: 0,\n                    x: -20\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: index * 0.1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-current rounded-full opacity-60\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `text-sm ${currentYuga.textColor} opacity-80`,\n                    children: char\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-sm ${currentYuga.textColor} opacity-70`,\n                children: [\"Duration: \", currentYuga.duration]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"mt-6 grid grid-cols-2 md:grid-cols-4 gap-4\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.3\n            },\n            children: Object.entries(worldState.environment).slice(0, 4).map(([aspect, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spiritual-card p-4 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl mb-2\",\n                children: getEnvironmentIcon(aspect, value)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-medium text-gray-700 capitalize mb-1\",\n                children: aspect\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-lg font-bold text-saffron-600\",\n                children: [value, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-200 rounded-full h-1.5 mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `h-1.5 rounded-full transition-all duration-500 ${aspect === 'conflict' ? 'bg-red-500' : 'bg-green-500'}`,\n                  style: {\n                    width: `${value}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this)]\n            }, aspect, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"mt-8\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-semibold text-saffron-700 mb-6\",\n              children: \"Sacred Locations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n              children: locations.map(location => /*#__PURE__*/_jsxDEV(motion.div, {\n                className: `spiritual-card p-6 cursor-pointer transition-all border ${location.available ? `hover:shadow-lg hover:scale-105 ${currentYuga.cardStyle}` : 'opacity-50 cursor-not-allowed border-gray-200'}`,\n                whileHover: location.available ? {\n                  y: -5\n                } : {},\n                onClick: () => {\n                  if (!location.available) return;\n                  if (location.special === 'temples') {\n                    setShowTemples(true);\n                  } else {\n                    setSelectedLocation(location.id);\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl\",\n                    children: location.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 342,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"font-semibold text-gray-800 mb-1\",\n                      children: location.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 mb-2\",\n                      children: location.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex space-x-4 text-xs\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-green-600\",\n                        children: [\"+\", location.benefits.karma, \" Karma\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 351,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-yellow-600\",\n                        children: [\"+\", location.benefits.sattva, \" Sattva\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this), !location.available && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-3 text-xs text-red-600\",\n                  children: \"Not available in current world state\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 23\n                }, this)]\n              }, location.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spiritual-card p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-4 text-saffron-700\",\n              children: \"Cosmic Cycles\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: Object.entries(yugaDescriptions).map(([yuga, info]) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleYugaTransition(yuga),\n                disabled: isTransitioning,\n                className: `w-full text-left p-3 rounded-lg border transition-all ${worldState.currentYuga === yuga ? 'border-saffron-300 bg-saffron-50' : 'border-gray-200 hover:border-saffron-200'} ${isTransitioning ? 'opacity-50 cursor-not-allowed' : ''}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl mr-2\",\n                    children: info.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-800\",\n                      children: info.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 393,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-600\",\n                      children: info.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 21\n                }, this)\n              }, yuga, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 flex justify-between items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500\",\n                children: \"* Affects world environment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowYugaDetails(true),\n                className: \"text-xs text-saffron-600 hover:text-saffron-700 font-medium\",\n                children: \"Learn More\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spiritual-card p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-4 text-saffron-700\",\n              children: \"Collective Karma\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-saffron-600 mb-2\",\n                children: worldState.collectiveKarma\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 mb-4\",\n                children: \"Combined karma of all souls\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-200 rounded-full h-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `h-3 rounded-full transition-all duration-500 ${worldState.collectiveKarma >= 0 ? 'bg-green-500' : 'bg-red-500'}`,\n                  style: {\n                    width: `${Math.min(100, Math.max(0, (worldState.collectiveKarma + 1000) / 20))}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spiritual-card p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-4 text-saffron-700\",\n              children: \"Cosmic Events\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 p-3 bg-blue-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(Sun, {\n                  className: \"w-5 h-5 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium\",\n                    children: \"Solar Eclipse\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-600\",\n                    children: \"Spiritual energy amplified\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(Mountain, {\n                  className: \"w-5 h-5 text-purple-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium\",\n                    children: \"Sacred Festival\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-600\",\n                    children: \"Bonus karma for good deeds\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this), avatar && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spiritual-card p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-4 text-saffron-700\",\n              children: \"Your Impact\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Locations Visited\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: avatar.stats.karma.recent.filter(a => a.context.includes('exploration')).length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 468,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"World Contribution\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-green-600\",\n                  children: [\"+\", Math.max(0, avatar.stats.karma.total), \" karma\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Spiritual Influence\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium\",\n                  children: avatar.stats.spiritualLevel > 50 ? 'High' : avatar.stats.spiritualLevel > 25 ? 'Medium' : 'Growing'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 467,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: selectedLocation && /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          exit: {\n            opacity: 0\n          },\n          onClick: () => setSelectedLocation(null),\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"spiritual-card p-8 max-w-md w-full\",\n            initial: {\n              scale: 0.9,\n              opacity: 0\n            },\n            animate: {\n              scale: 1,\n              opacity: 1\n            },\n            exit: {\n              scale: 0.9,\n              opacity: 0\n            },\n            onClick: e => e.stopPropagation(),\n            children: (() => {\n              const location = locations.find(l => l.id === selectedLocation);\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl mb-4\",\n                  children: location.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold text-saffron-700 mb-2\",\n                  children: location.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 mb-6\",\n                  children: location.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center space-x-6 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-2xl font-bold text-green-600\",\n                      children: [\"+\", location.benefits.karma]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 523,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-600\",\n                      children: \"Karma\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 526,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-2xl font-bold text-yellow-600\",\n                      children: [\"+\", location.benefits.sattva]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-600\",\n                      children: \"Sattva\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 532,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 528,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => setSelectedLocation(null),\n                    className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50\",\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleLocationVisit(location),\n                    className: \"flex-1 karma-button\",\n                    children: \"Visit\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 21\n              }, this);\n            })()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: showTemples && /*#__PURE__*/_jsxDEV(TempleList, {\n          onClose: () => setShowTemples(false),\n          onVisit: handleTempleVisit\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: showYugaDetails && /*#__PURE__*/_jsxDEV(YugaDetails, {\n          onClose: () => setShowYugaDetails(false),\n          onTransition: handleYugaTransition\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 568,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: isTransitioning && /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"fixed inset-0 bg-white/90 flex items-center justify-center z-50\",\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          exit: {\n            opacity: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [yugaAnimation === 'satya' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"text-8xl mb-4\",\n              animate: {\n                scale: [1, 1.2, 1],\n                rotate: [0, 10, -10, 0]\n              },\n              transition: {\n                duration: 2,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              },\n              children: \"\\uD83D\\uDD49\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 19\n            }, this), yugaAnimation === 'treta' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"text-8xl mb-4\",\n              animate: {\n                scale: [1, 1.1, 1],\n                rotate: [0, 5, -5, 0]\n              },\n              transition: {\n                duration: 1.5,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              },\n              children: \"\\uD83D\\uDD25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 19\n            }, this), yugaAnimation === 'dvapara' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"text-8xl mb-4\",\n              animate: {\n                scale: [1, 1.05, 1],\n                rotate: [0, 3, -3, 0]\n              },\n              transition: {\n                duration: 1.2,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              },\n              children: \"\\u2694\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 19\n            }, this), yugaAnimation === 'kali' && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"text-8xl mb-4\",\n              animate: {\n                scale: [1, 1.15, 1],\n                rotate: [0, 20, -20, 0]\n              },\n              transition: {\n                duration: 1,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              },\n              children: \"\\uFFFD\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-saffron-700 mb-2\",\n              children: \"Cosmic Transition\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"The world is shifting between yugas...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 200,\n    columnNumber: 5\n  }, this);\n};\n_s(WorldExplorer, \"/Ks1pjGKfcdBpjaAtKHa3uHdKns=\", false, function () {\n  return [useGameStore];\n});\n_c = WorldExplorer;\nexport default WorldExplorer;\nvar _c;\n$RefreshReg$(_c, \"WorldExplorer\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "motion", "AnimatePresence", "useGameStore", "Sun", "Mountain", "TempleList", "YugaDetails", "jsxDEV", "_jsxDEV", "WorldExplorer", "_s", "worldState", "avatar", "transitionYuga", "updateKarma", "updateVirtues", "addNotification", "selectedLocation", "setSelectedLocation", "isTransitioning", "setIsTransitioning", "showTemples", "setShowTemples", "showYugaDetails", "setShowYugaDetails", "yugaAnimation", "setYugaAnimation", "omChantingRef", "whySoSeriousRef", "yugaDescriptions", "satya", "name", "title", "description", "characteristics", "duration", "color", "textColor", "bgPattern", "icon", "cardStyle", "treta", "dva<PERSON>a", "kali", "locations", "id", "benefits", "karma", "sattva", "available", "special", "environment", "spirituality", "harmony", "prosperity", "currentYuga", "handleLocationVisit", "location", "Date", "now", "action", "karmaChange", "gunaEffect", "timestamp", "context", "handleTempleVisit", "temple", "devotion", "handleYugaTransition", "newYuga", "includes", "_whySoSeriousRef$curr", "_omChantingRef$curren", "current", "pause", "currentTime", "play", "_omChantingRef$curren2", "_whySoSeriousRef$curr2", "setTimeout", "getEnvironmentIcon", "aspect", "value", "icons", "conflict", "className", "style", "backgroundImage", "backgroundColor", "children", "ref", "src", "preload", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "scale", "map", "char", "index", "x", "transition", "delay", "Object", "entries", "slice", "width", "whileHover", "onClick", "yuga", "info", "disabled", "collectiveKarma", "Math", "min", "max", "stats", "recent", "filter", "a", "length", "total", "spiritualLevel", "exit", "e", "stopPropagation", "find", "l", "onClose", "onVisit", "onTransition", "rotate", "repeat", "Infinity", "ease", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Master1/karmaverse/src/components/World/WorldExplorer.tsx"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useGameStore } from '../../store/gameStore';\nimport { audioService } from '../../services/audioService';\nimport { Yuga } from '../../types';\nimport { Sun, Mountain } from 'lucide-react';\nimport TempleList from './TempleList';\nimport YugaDetails from './YugaDetails';\n\nconst WorldExplorer: React.FC = () => {\n  const { worldState, avatar, transitionYuga, updateKarma, updateVirtues, addNotification } = useGameStore();\n  const [selectedLocation, setSelectedLocation] = useState<string | null>(null);\n  const [isTransitioning, setIsTransitioning] = useState(false);\n  const [showTemples, setShowTemples] = useState(false);\n  const [showYugaDetails, setShowYugaDetails] = useState(false);\n  const [yugaAnimation, setYugaAnimation] = useState<string | null>(null);\n\n  // Audio refs for Yuga music\n  const omChantingRef = useRef<HTMLAudioElement>(null);\n  const whySoSeriousRef = useRef<HTMLAudioElement>(null);\n\n  const yugaDescriptions = {\n    satya: {\n      name: 'Satya Yuga',\n      title: 'The Golden Age of Truth',\n      description: 'An age of perfect virtue, where dharma reigns supreme and all beings live in harmony with divine law.',\n      characteristics: ['Perfect righteousness', 'No suffering or disease', 'Natural abundance', 'Divine communion'],\n      duration: '1,728,000 years',\n      color: 'from-yellow-200 to-green-200',\n      textColor: 'text-green-800',\n      bgPattern: \"url(\\\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23228B22' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n      icon: '🌅',\n      cardStyle: 'border-green-300 shadow-green-200/50'\n    },\n    treta: {\n      name: 'Treta Yuga',\n      title: 'The Silver Age of Sacrifice',\n      description: 'Virtue begins to decline, but dharma is still strong. Great sacrifices and rituals maintain cosmic order.',\n      characteristics: ['Three-quarters virtue', 'Introduction of rituals', 'Emergence of kingdoms', 'Heroic deeds'],\n      duration: '1,296,000 years',\n      color: 'from-yellow-100 to-orange-200',\n      textColor: 'text-orange-800',\n      bgPattern: \"url(\\\"data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ed7611' fill-opacity='0.1'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n      icon: '🌞',\n      cardStyle: 'border-orange-300 shadow-orange-200/50'\n    },\n    dvapara: {\n      name: 'Dvapara Yuga',\n      title: 'The Bronze Age of Duality',\n      description: 'Virtue and vice are equally balanced. Knowledge becomes divided and conflicts arise between good and evil.',\n      characteristics: ['Half virtue remains', 'Division of knowledge', 'Rise of conflicts', 'Material progress'],\n      duration: '864,000 years',\n      color: 'from-blue-100 to-purple-200',\n      textColor: 'text-purple-800',\n      bgPattern: \"url(\\\"data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Cpath d='M0 0h20L0 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\\\")\",\n      icon: '🌆',\n      cardStyle: 'border-purple-300 shadow-purple-200/50'\n    },\n    kali: {\n      name: 'Kali Yuga',\n      title: 'The Iron Age of Darkness',\n      description: 'The age of darkness and materialism. Virtue is at its lowest, but spiritual liberation is most accessible.',\n      characteristics: ['Quarter virtue remains', 'Widespread ignorance', 'Material obsession', 'Easy liberation'],\n      duration: '432,000 years',\n      color: 'from-gray-200 to-gray-400',\n      textColor: 'text-gray-800',\n      bgPattern: \"url(\\\"data:image/svg+xml,%3Csvg width='6' height='6' viewBox='0 0 6 6' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cpath d='M5 0h1L0 5v1H5z'/%3E%3C/g%3E%3C/svg%3E\\\")\",\n      icon: '🌃',\n      cardStyle: 'border-gray-300 shadow-gray-200/50'\n    }\n  };\n\n  const locations = [\n    {\n      id: 'temple',\n      name: 'Sacred Temples',\n      icon: '🏛️',\n      description: 'Visit ancient Hindu temples across India',\n      benefits: { karma: 10, sattva: 5 },\n      available: true,\n      special: 'temples'\n    },\n    {\n      id: 'forest',\n      name: 'Enchanted Forest',\n      icon: '🌲',\n      description: 'Meditate among ancient trees and connect with nature',\n      benefits: { karma: 8, sattva: 8 },\n      available: worldState.environment.spirituality > 30\n    },\n    {\n      id: 'mountain',\n      name: 'Sacred Mountain',\n      icon: '⛰️',\n      description: 'Climb to spiritual heights and gain wisdom',\n      benefits: { karma: 15, sattva: 10 },\n      available: worldState.environment.harmony > 50\n    },\n    {\n      id: 'river',\n      name: 'Holy River',\n      icon: '🌊',\n      description: 'Purify your soul in sacred waters',\n      benefits: { karma: 12, sattva: 7 },\n      available: true\n    },\n    {\n      id: 'village',\n      name: 'Dharmic Village',\n      icon: '🏘️',\n      description: 'Serve the community and practice karma yoga',\n      benefits: { karma: 20, sattva: 3 },\n      available: worldState.environment.prosperity > 40\n    },\n    {\n      id: 'cave',\n      name: 'Meditation Cave',\n      icon: '🕳️',\n      description: 'Deep contemplation in solitude',\n      benefits: { karma: 5, sattva: 15 },\n      available: worldState.currentYuga !== 'kali'\n    }\n  ];\n\n  const handleLocationVisit = (location: typeof locations[0]) => {\n    if (!avatar || !location.available) return;\n\n    updateKarma({\n      id: `location_${Date.now()}`,\n      action: `Visited ${location.name}`,\n      karmaChange: location.benefits.karma,\n      gunaEffect: { sattva: location.benefits.sattva },\n      timestamp: new Date(),\n      context: `World exploration in ${worldState.currentYuga} Yuga`\n    });\n\n    addNotification(`Visited ${location.name}: +${location.benefits.karma} karma, +${location.benefits.sattva} sattva`);\n    setSelectedLocation(null);\n  };\n  \n  const handleTempleVisit = (temple: any) => {\n    if (!avatar) return;\n    \n    updateKarma({\n      id: `temple_${Date.now()}`,\n      action: `Visited ${temple.name}`,\n      karmaChange: temple.benefits.karma,\n      gunaEffect: { sattva: temple.benefits.sattva },\n      timestamp: new Date(),\n      context: `Temple pilgrimage in ${worldState.currentYuga} Yuga`\n    });\n    \n    // Update devotion virtue\n    updateVirtues({\n      devotion: temple.benefits.devotion / 5 // Scale down for balance\n    });\n    \n    addNotification(`Visited ${temple.name}: +${temple.benefits.karma} karma, +${temple.benefits.sattva} sattva, +${temple.benefits.devotion / 5} devotion`);\n    setShowTemples(false);\n  };\n\n  const handleYugaTransition = (newYuga: Yuga) => {\n    if (newYuga === worldState.currentYuga) return;\n\n    setIsTransitioning(true);\n\n    // Set animation type for the Yuga\n    setYugaAnimation(newYuga);\n\n    // Play the appropriate music\n    if (['satya', 'treta', 'dvapara'].includes(newYuga)) {\n      whySoSeriousRef.current?.pause();\n      whySoSeriousRef.current && (whySoSeriousRef.current.currentTime = 0);\n      omChantingRef.current?.play();\n    } else if (newYuga === 'kali') {\n      omChantingRef.current?.pause();\n      omChantingRef.current && (omChantingRef.current.currentTime = 0);\n      whySoSeriousRef.current?.play();\n    }\n\n    setTimeout(() => {\n      transitionYuga(newYuga);\n      setIsTransitioning(false);\n    }, 1000);\n  };\n\n  const getEnvironmentIcon = (aspect: string, value: number) => {\n    const icons = {\n      harmony: value > 70 ? '☮️' : value > 40 ? '⚖️' : '⚔️',\n      prosperity: value > 70 ? '💰' : value > 40 ? '🏠' : '🍞',\n      spirituality: value > 70 ? '🕉️' : value > 40 ? '🙏' : '📿',\n      conflict: value > 70 ? '⚔️' : value > 40 ? '🤝' : '☮️'\n    };\n    return icons[aspect as keyof typeof icons] || '❓';\n  };\n\n  const currentYuga = yugaDescriptions[worldState.currentYuga];\n\n  return (\n    <div className=\"min-h-screen pt-20 p-6\" style={{ \n      backgroundImage: currentYuga.bgPattern,\n      backgroundColor: worldState.currentYuga === 'satya' ? '#f0fff4' :\n                      worldState.currentYuga === 'treta' ? '#fffaf0' :\n                      worldState.currentYuga === 'dvapara' ? '#f5f0ff' : '#f5f5f5'\n    }}>\n      {/* Audio elements for Yuga music */}\n      <audio ref={omChantingRef} src=\"/One%20minute%20Om%20Chanting.mp3\" preload=\"auto\" />\n      <audio ref={whySoSeriousRef} src=\"/Hans%20Zimmer%20&%20James%20Newton%20Howard%20-%20Why%20So%20Serious_%20(Official%20Audio).mp3\" preload=\"auto\" />\n\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Header */}\n        <motion.div\n          className=\"text-center mb-8\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n        >\n          <h1 className=\"text-4xl font-spiritual font-bold text-saffron-700 mb-2\">\n            World Explorer\n          </h1>\n          <p className=\"text-gray-600\">\n            Journey through the cosmic cycles and sacred locations\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Current Yuga Display */}\n          <motion.div\n            className=\"lg:col-span-2\"\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n          >\n            <div className={`spiritual-card p-8 bg-gradient-to-br ${currentYuga.color} relative overflow-hidden border ${currentYuga.cardStyle} transition-all duration-500`}>\n              {/* Background Pattern */}\n              <div className=\"absolute inset-0 opacity-10\">\n                <div className=\"w-full h-full\" style={{\n                  backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n                }} />\n              </div>\n\n              <div className=\"relative z-10\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <div>\n                    <h2 className={`text-3xl font-bold ${currentYuga.textColor} mb-2`}>\n                      {currentYuga.name}\n                    </h2>\n                    <p className={`text-lg ${currentYuga.textColor} opacity-80`}>\n                      {currentYuga.title}\n                    </p>\n                  </div>\n                  <div className=\"text-6xl\">\n                    {currentYuga.icon}\n                  </div>\n                </div>\n\n                <p className={`${currentYuga.textColor} opacity-90 mb-6 leading-relaxed`}>\n                  {currentYuga.description}\n                </p>\n\n                <div className=\"grid grid-cols-2 gap-4 mb-6\">\n                  {currentYuga.characteristics.map((char, index) => (\n                    <motion.div\n                      key={index}\n                      className=\"flex items-center space-x-2\"\n                      initial={{ opacity: 0, x: -20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      transition={{ delay: index * 0.1 }}\n                    >\n                      <div className=\"w-2 h-2 bg-current rounded-full opacity-60\" />\n                      <span className={`text-sm ${currentYuga.textColor} opacity-80`}>\n                        {char}\n                      </span>\n                    </motion.div>\n                  ))}\n                </div>\n\n                <div className={`text-sm ${currentYuga.textColor} opacity-70`}>\n                  Duration: {currentYuga.duration}\n                </div>\n              </div>\n            </div>\n\n            {/* Environment Status */}\n            <motion.div\n              className=\"mt-6 grid grid-cols-2 md:grid-cols-4 gap-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.3 }}\n            >\n              {Object.entries(worldState.environment).slice(0, 4).map(([aspect, value]) => (\n                <div key={aspect} className=\"spiritual-card p-4 text-center\">\n                  <div className=\"text-3xl mb-2\">\n                    {getEnvironmentIcon(aspect, value)}\n                  </div>\n                  <div className=\"text-sm font-medium text-gray-700 capitalize mb-1\">\n                    {aspect}\n                  </div>\n                  <div className=\"text-lg font-bold text-saffron-600\">\n                    {value}%\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-1.5 mt-2\">\n                    <div\n                      className={`h-1.5 rounded-full transition-all duration-500 ${\n                        aspect === 'conflict' ? 'bg-red-500' : 'bg-green-500'\n                      }`}\n                      style={{ width: `${value}%` }}\n                    />\n                  </div>\n                </div>\n              ))}\n            </motion.div>\n\n            {/* Sacred Locations */}\n            <motion.div\n              className=\"mt-8\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.5 }}\n            >\n              <h3 className=\"text-2xl font-semibold text-saffron-700 mb-6\">\n                Sacred Locations\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {locations.map((location) => (\n                  <motion.div\n                    key={location.id}\n                    className={`spiritual-card p-6 cursor-pointer transition-all border ${\n                      location.available\n                        ? `hover:shadow-lg hover:scale-105 ${currentYuga.cardStyle}`\n                        : 'opacity-50 cursor-not-allowed border-gray-200'\n                    }`}\n                    whileHover={location.available ? { y: -5 } : {}}\n                    onClick={() => {\n                      if (!location.available) return;\n                      if (location.special === 'temples') {\n                        setShowTemples(true);\n                      } else {\n                        setSelectedLocation(location.id);\n                      }\n                    }}\n                  >\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"text-4xl\">{location.icon}</div>\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-semibold text-gray-800 mb-1\">\n                          {location.name}\n                        </h4>\n                        <p className=\"text-sm text-gray-600 mb-2\">\n                          {location.description}\n                        </p>\n                        <div className=\"flex space-x-4 text-xs\">\n                          <span className=\"text-green-600\">\n                            +{location.benefits.karma} Karma\n                          </span>\n                          <span className=\"text-yellow-600\">\n                            +{location.benefits.sattva} Sattva\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                    {!location.available && (\n                      <div className=\"mt-3 text-xs text-red-600\">\n                        Not available in current world state\n                      </div>\n                    )}\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          </motion.div>\n\n          {/* Sidebar */}\n          <div className=\"space-y-6\">\n            {/* Yuga Selector (Debug/Admin) */}\n            <div className=\"spiritual-card p-6\">\n              <h3 className=\"text-lg font-semibold mb-4 text-saffron-700\">\n                Cosmic Cycles\n              </h3>\n              <div className=\"space-y-2\">\n                {Object.entries(yugaDescriptions).map(([yuga, info]) => (\n                  <button\n                    key={yuga}\n                    onClick={() => handleYugaTransition(yuga as Yuga)}\n                    disabled={isTransitioning}\n                    className={`w-full text-left p-3 rounded-lg border transition-all ${\n                      worldState.currentYuga === yuga\n                        ? 'border-saffron-300 bg-saffron-50'\n                        : 'border-gray-200 hover:border-saffron-200'\n                    } ${isTransitioning ? 'opacity-50 cursor-not-allowed' : ''}`}\n                  >\n                    <div className=\"flex items-center\">\n                      <span className=\"text-2xl mr-2\">{info.icon}</span>\n                      <div>\n                        <div className=\"font-medium text-gray-800\">{info.name}</div>\n                        <div className=\"text-xs text-gray-600\">{info.title}</div>\n                      </div>\n                    </div>\n                  </button>\n                ))}\n              </div>\n              <div className=\"mt-4 flex justify-between items-center\">\n                <div className=\"text-xs text-gray-500\">\n                  * Affects world environment\n                </div>\n                <button \n                  onClick={() => setShowYugaDetails(true)}\n                  className=\"text-xs text-saffron-600 hover:text-saffron-700 font-medium\"\n                >\n                  Learn More\n                </button>\n              </div>\n            </div>\n\n            {/* Collective Karma */}\n            <div className=\"spiritual-card p-6\">\n              <h3 className=\"text-lg font-semibold mb-4 text-saffron-700\">\n                Collective Karma\n              </h3>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-saffron-600 mb-2\">\n                  {worldState.collectiveKarma}\n                </div>\n                <div className=\"text-sm text-gray-600 mb-4\">\n                  Combined karma of all souls\n                </div>\n                <div className=\"w-full bg-gray-200 rounded-full h-3\">\n                  <div\n                    className={`h-3 rounded-full transition-all duration-500 ${\n                      worldState.collectiveKarma >= 0 ? 'bg-green-500' : 'bg-red-500'\n                    }`}\n                    style={{\n                      width: `${Math.min(100, Math.max(0, (worldState.collectiveKarma + 1000) / 20))}%`\n                    }}\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* World Events */}\n            <div className=\"spiritual-card p-6\">\n              <h3 className=\"text-lg font-semibold mb-4 text-saffron-700\">\n                Cosmic Events\n              </h3>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center space-x-3 p-3 bg-blue-50 rounded-lg\">\n                  <Sun className=\"w-5 h-5 text-blue-600\" />\n                  <div>\n                    <div className=\"text-sm font-medium\">Solar Eclipse</div>\n                    <div className=\"text-xs text-gray-600\">Spiritual energy amplified</div>\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-3 p-3 bg-purple-50 rounded-lg\">\n                  <Mountain className=\"w-5 h-5 text-purple-600\" />\n                  <div>\n                    <div className=\"text-sm font-medium\">Sacred Festival</div>\n                    <div className=\"text-xs text-gray-600\">Bonus karma for good deeds</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Quick Stats */}\n            {avatar && (\n              <div className=\"spiritual-card p-6\">\n                <h3 className=\"text-lg font-semibold mb-4 text-saffron-700\">\n                  Your Impact\n                </h3>\n                <div className=\"space-y-3\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-sm text-gray-600\">Locations Visited</span>\n                    <span className=\"font-medium\">\n                      {avatar.stats.karma.recent.filter(a => a.context.includes('exploration')).length}\n                    </span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-sm text-gray-600\">World Contribution</span>\n                    <span className=\"font-medium text-green-600\">\n                      +{Math.max(0, avatar.stats.karma.total)} karma\n                    </span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-sm text-gray-600\">Spiritual Influence</span>\n                    <span className=\"font-medium\">\n                      {avatar.stats.spiritualLevel > 50 ? 'High' : \n                       avatar.stats.spiritualLevel > 25 ? 'Medium' : 'Growing'}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Location Visit Modal */}\n        <AnimatePresence>\n          {selectedLocation && (\n            <motion.div\n              className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n              onClick={() => setSelectedLocation(null)}\n            >\n              <motion.div\n                className=\"spiritual-card p-8 max-w-md w-full\"\n                initial={{ scale: 0.9, opacity: 0 }}\n                animate={{ scale: 1, opacity: 1 }}\n                exit={{ scale: 0.9, opacity: 0 }}\n                onClick={(e) => e.stopPropagation()}\n              >\n                {(() => {\n                  const location = locations.find(l => l.id === selectedLocation)!;\n                  return (\n                    <div className=\"text-center\">\n                      <div className=\"text-6xl mb-4\">{location.icon}</div>\n                      <h3 className=\"text-2xl font-bold text-saffron-700 mb-2\">\n                        {location.name}\n                      </h3>\n                      <p className=\"text-gray-600 mb-6\">\n                        {location.description}\n                      </p>\n                      <div className=\"flex justify-center space-x-6 mb-6\">\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl font-bold text-green-600\">\n                            +{location.benefits.karma}\n                          </div>\n                          <div className=\"text-sm text-gray-600\">Karma</div>\n                        </div>\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl font-bold text-yellow-600\">\n                            +{location.benefits.sattva}\n                          </div>\n                          <div className=\"text-sm text-gray-600\">Sattva</div>\n                        </div>\n                      </div>\n                      <div className=\"flex space-x-3\">\n                        <button\n                          onClick={() => setSelectedLocation(null)}\n                          className=\"flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50\"\n                        >\n                          Cancel\n                        </button>\n                        <button\n                          onClick={() => handleLocationVisit(location)}\n                          className=\"flex-1 karma-button\"\n                        >\n                          Visit\n                        </button>\n                      </div>\n                    </div>\n                  );\n                })()}\n              </motion.div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {/* Temple List Modal */}\n        <AnimatePresence>\n          {showTemples && (\n            <TempleList \n              onClose={() => setShowTemples(false)}\n              onVisit={handleTempleVisit}\n            />\n          )}\n        </AnimatePresence>\n        \n        {/* Yuga Details Modal */}\n        <AnimatePresence>\n          {showYugaDetails && (\n            <YugaDetails \n              onClose={() => setShowYugaDetails(false)}\n              onTransition={handleYugaTransition}\n            />\n          )}\n        </AnimatePresence>\n        \n        {/* Transition Overlay */}\n        <AnimatePresence>\n          {isTransitioning && (\n            <motion.div\n              className=\"fixed inset-0 bg-white/90 flex items-center justify-center z-50\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n            >\n              <div className=\"text-center\">\n                {/* Yuga-specific animation */}\n                {yugaAnimation === 'satya' && (\n                  <motion.div\n                    className=\"text-8xl mb-4\"\n                    animate={{ scale: [1, 1.2, 1], rotate: [0, 10, -10, 0] }}\n                    transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n                  >\n                    🕉️\n                  </motion.div>\n                )}\n                {yugaAnimation === 'treta' && (\n                  <motion.div\n                    className=\"text-8xl mb-4\"\n                    animate={{ scale: [1, 1.1, 1], rotate: [0, 5, -5, 0] }}\n                    transition={{ duration: 1.5, repeat: Infinity, ease: \"easeInOut\" }}\n                  >\n                    🔥\n                  </motion.div>\n                )}\n                {yugaAnimation === 'dvapara' && (\n                  <motion.div\n                    className=\"text-8xl mb-4\"\n                    animate={{ scale: [1, 1.05, 1], rotate: [0, 3, -3, 0] }}\n                    transition={{ duration: 1.2, repeat: Infinity, ease: \"easeInOut\" }}\n                  >\n                    ⚔️\n                  </motion.div>\n                )}\n                {yugaAnimation === 'kali' && (\n                  <motion.div\n                    className=\"text-8xl mb-4\"\n                    animate={{ scale: [1, 1.15, 1], rotate: [0, 20, -20, 0] }}\n                    transition={{ duration: 1, repeat: Infinity, ease: \"easeInOut\" }}\n                  >\n                    �️\n                  </motion.div>\n                )}\n                <h2 className=\"text-2xl font-bold text-saffron-700 mb-2\">\n                  Cosmic Transition\n                </h2>\n                <p className=\"text-gray-600\">\n                  The world is shifting between yugas...\n                </p>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </div>\n  );\n};\n\nexport default WorldExplorer;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,YAAY,QAAQ,uBAAuB;AAGpD,SAASC,GAAG,EAAEC,QAAQ,QAAQ,cAAc;AAC5C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM;IAAEC,UAAU;IAAEC,MAAM;IAAEC,cAAc;IAAEC,WAAW;IAAEC,aAAa;IAAEC;EAAgB,CAAC,GAAGd,YAAY,CAAC,CAAC;EAC1G,MAAM,CAACe,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;EAC7E,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAgB,IAAI,CAAC;;EAEvE;EACA,MAAM6B,aAAa,GAAG5B,MAAM,CAAmB,IAAI,CAAC;EACpD,MAAM6B,eAAe,GAAG7B,MAAM,CAAmB,IAAI,CAAC;EAEtD,MAAM8B,gBAAgB,GAAG;IACvBC,KAAK,EAAE;MACLC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,yBAAyB;MAChCC,WAAW,EAAE,uGAAuG;MACpHC,eAAe,EAAE,CAAC,uBAAuB,EAAE,yBAAyB,EAAE,mBAAmB,EAAE,kBAAkB,CAAC;MAC9GC,QAAQ,EAAE,iBAAiB;MAC3BC,KAAK,EAAE,8BAA8B;MACrCC,SAAS,EAAE,gBAAgB;MAC3BC,SAAS,EAAE,qXAAqX;MAChYC,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE;IACb,CAAC;IACDC,KAAK,EAAE;MACLV,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,6BAA6B;MACpCC,WAAW,EAAE,2GAA2G;MACxHC,eAAe,EAAE,CAAC,uBAAuB,EAAE,yBAAyB,EAAE,uBAAuB,EAAE,cAAc,CAAC;MAC9GC,QAAQ,EAAE,iBAAiB;MAC3BC,KAAK,EAAE,+BAA+B;MACtCC,SAAS,EAAE,iBAAiB;MAC5BC,SAAS,EAAE,yfAAyf;MACpgBC,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE;IACb,CAAC;IACDE,OAAO,EAAE;MACPX,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,2BAA2B;MAClCC,WAAW,EAAE,4GAA4G;MACzHC,eAAe,EAAE,CAAC,qBAAqB,EAAE,uBAAuB,EAAE,mBAAmB,EAAE,mBAAmB,CAAC;MAC3GC,QAAQ,EAAE,eAAe;MACzBC,KAAK,EAAE,6BAA6B;MACpCC,SAAS,EAAE,iBAAiB;MAC5BC,SAAS,EAAE,8PAA8P;MACzQC,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE;IACb,CAAC;IACDG,IAAI,EAAE;MACJZ,IAAI,EAAE,WAAW;MACjBC,KAAK,EAAE,0BAA0B;MACjCC,WAAW,EAAE,4GAA4G;MACzHC,eAAe,EAAE,CAAC,wBAAwB,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,iBAAiB,CAAC;MAC5GC,QAAQ,EAAE,eAAe;MACzBC,KAAK,EAAE,2BAA2B;MAClCC,SAAS,EAAE,eAAe;MAC1BC,SAAS,EAAE,iOAAiO;MAC5OC,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE;IACb;EACF,CAAC;EAED,MAAMI,SAAS,GAAG,CAChB;IACEC,EAAE,EAAE,QAAQ;IACZd,IAAI,EAAE,gBAAgB;IACtBQ,IAAI,EAAE,KAAK;IACXN,WAAW,EAAE,0CAA0C;IACvDa,QAAQ,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAE,CAAC;IAClCC,SAAS,EAAE,IAAI;IACfC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,EAAE,EAAE,QAAQ;IACZd,IAAI,EAAE,kBAAkB;IACxBQ,IAAI,EAAE,IAAI;IACVN,WAAW,EAAE,sDAAsD;IACnEa,QAAQ,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IACjCC,SAAS,EAAEtC,UAAU,CAACwC,WAAW,CAACC,YAAY,GAAG;EACnD,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdd,IAAI,EAAE,iBAAiB;IACvBQ,IAAI,EAAE,IAAI;IACVN,WAAW,EAAE,4CAA4C;IACzDa,QAAQ,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAG,CAAC;IACnCC,SAAS,EAAEtC,UAAU,CAACwC,WAAW,CAACE,OAAO,GAAG;EAC9C,CAAC,EACD;IACER,EAAE,EAAE,OAAO;IACXd,IAAI,EAAE,YAAY;IAClBQ,IAAI,EAAE,IAAI;IACVN,WAAW,EAAE,mCAAmC;IAChDa,QAAQ,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAE,CAAC;IAClCC,SAAS,EAAE;EACb,CAAC,EACD;IACEJ,EAAE,EAAE,SAAS;IACbd,IAAI,EAAE,iBAAiB;IACvBQ,IAAI,EAAE,KAAK;IACXN,WAAW,EAAE,6CAA6C;IAC1Da,QAAQ,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAE,CAAC;IAClCC,SAAS,EAAEtC,UAAU,CAACwC,WAAW,CAACG,UAAU,GAAG;EACjD,CAAC,EACD;IACET,EAAE,EAAE,MAAM;IACVd,IAAI,EAAE,iBAAiB;IACvBQ,IAAI,EAAE,KAAK;IACXN,WAAW,EAAE,gCAAgC;IAC7Ca,QAAQ,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAG,CAAC;IAClCC,SAAS,EAAEtC,UAAU,CAAC4C,WAAW,KAAK;EACxC,CAAC,CACF;EAED,MAAMC,mBAAmB,GAAIC,QAA6B,IAAK;IAC7D,IAAI,CAAC7C,MAAM,IAAI,CAAC6C,QAAQ,CAACR,SAAS,EAAE;IAEpCnC,WAAW,CAAC;MACV+B,EAAE,EAAE,YAAYa,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC5BC,MAAM,EAAE,WAAWH,QAAQ,CAAC1B,IAAI,EAAE;MAClC8B,WAAW,EAAEJ,QAAQ,CAACX,QAAQ,CAACC,KAAK;MACpCe,UAAU,EAAE;QAAEd,MAAM,EAAES,QAAQ,CAACX,QAAQ,CAACE;MAAO,CAAC;MAChDe,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC;MACrBM,OAAO,EAAE,wBAAwBrD,UAAU,CAAC4C,WAAW;IACzD,CAAC,CAAC;IAEFvC,eAAe,CAAC,WAAWyC,QAAQ,CAAC1B,IAAI,MAAM0B,QAAQ,CAACX,QAAQ,CAACC,KAAK,YAAYU,QAAQ,CAACX,QAAQ,CAACE,MAAM,SAAS,CAAC;IACnH9B,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM+C,iBAAiB,GAAIC,MAAW,IAAK;IACzC,IAAI,CAACtD,MAAM,EAAE;IAEbE,WAAW,CAAC;MACV+B,EAAE,EAAE,UAAUa,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC1BC,MAAM,EAAE,WAAWM,MAAM,CAACnC,IAAI,EAAE;MAChC8B,WAAW,EAAEK,MAAM,CAACpB,QAAQ,CAACC,KAAK;MAClCe,UAAU,EAAE;QAAEd,MAAM,EAAEkB,MAAM,CAACpB,QAAQ,CAACE;MAAO,CAAC;MAC9Ce,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC;MACrBM,OAAO,EAAE,wBAAwBrD,UAAU,CAAC4C,WAAW;IACzD,CAAC,CAAC;;IAEF;IACAxC,aAAa,CAAC;MACZoD,QAAQ,EAAED,MAAM,CAACpB,QAAQ,CAACqB,QAAQ,GAAG,CAAC,CAAC;IACzC,CAAC,CAAC;IAEFnD,eAAe,CAAC,WAAWkD,MAAM,CAACnC,IAAI,MAAMmC,MAAM,CAACpB,QAAQ,CAACC,KAAK,YAAYmB,MAAM,CAACpB,QAAQ,CAACE,MAAM,aAAakB,MAAM,CAACpB,QAAQ,CAACqB,QAAQ,GAAG,CAAC,WAAW,CAAC;IACxJ7C,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAM8C,oBAAoB,GAAIC,OAAa,IAAK;IAC9C,IAAIA,OAAO,KAAK1D,UAAU,CAAC4C,WAAW,EAAE;IAExCnC,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACAM,gBAAgB,CAAC2C,OAAO,CAAC;;IAEzB;IACA,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,CAACC,QAAQ,CAACD,OAAO,CAAC,EAAE;MAAA,IAAAE,qBAAA,EAAAC,qBAAA;MACnD,CAAAD,qBAAA,GAAA3C,eAAe,CAAC6C,OAAO,cAAAF,qBAAA,uBAAvBA,qBAAA,CAAyBG,KAAK,CAAC,CAAC;MAChC9C,eAAe,CAAC6C,OAAO,KAAK7C,eAAe,CAAC6C,OAAO,CAACE,WAAW,GAAG,CAAC,CAAC;MACpE,CAAAH,qBAAA,GAAA7C,aAAa,CAAC8C,OAAO,cAAAD,qBAAA,uBAArBA,qBAAA,CAAuBI,IAAI,CAAC,CAAC;IAC/B,CAAC,MAAM,IAAIP,OAAO,KAAK,MAAM,EAAE;MAAA,IAAAQ,sBAAA,EAAAC,sBAAA;MAC7B,CAAAD,sBAAA,GAAAlD,aAAa,CAAC8C,OAAO,cAAAI,sBAAA,uBAArBA,sBAAA,CAAuBH,KAAK,CAAC,CAAC;MAC9B/C,aAAa,CAAC8C,OAAO,KAAK9C,aAAa,CAAC8C,OAAO,CAACE,WAAW,GAAG,CAAC,CAAC;MAChE,CAAAG,sBAAA,GAAAlD,eAAe,CAAC6C,OAAO,cAAAK,sBAAA,uBAAvBA,sBAAA,CAAyBF,IAAI,CAAC,CAAC;IACjC;IAEAG,UAAU,CAAC,MAAM;MACflE,cAAc,CAACwD,OAAO,CAAC;MACvBjD,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAM4D,kBAAkB,GAAGA,CAACC,MAAc,EAAEC,KAAa,KAAK;IAC5D,MAAMC,KAAK,GAAG;MACZ9B,OAAO,EAAE6B,KAAK,GAAG,EAAE,GAAG,IAAI,GAAGA,KAAK,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;MACrD5B,UAAU,EAAE4B,KAAK,GAAG,EAAE,GAAG,IAAI,GAAGA,KAAK,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;MACxD9B,YAAY,EAAE8B,KAAK,GAAG,EAAE,GAAG,KAAK,GAAGA,KAAK,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;MAC3DE,QAAQ,EAAEF,KAAK,GAAG,EAAE,GAAG,IAAI,GAAGA,KAAK,GAAG,EAAE,GAAG,IAAI,GAAG;IACpD,CAAC;IACD,OAAOC,KAAK,CAACF,MAAM,CAAuB,IAAI,GAAG;EACnD,CAAC;EAED,MAAM1B,WAAW,GAAG1B,gBAAgB,CAAClB,UAAU,CAAC4C,WAAW,CAAC;EAE5D,oBACE/C,OAAA;IAAK6E,SAAS,EAAC,wBAAwB;IAACC,KAAK,EAAE;MAC7CC,eAAe,EAAEhC,WAAW,CAACjB,SAAS;MACtCkD,eAAe,EAAE7E,UAAU,CAAC4C,WAAW,KAAK,OAAO,GAAG,SAAS,GAC/C5C,UAAU,CAAC4C,WAAW,KAAK,OAAO,GAAG,SAAS,GAC9C5C,UAAU,CAAC4C,WAAW,KAAK,SAAS,GAAG,SAAS,GAAG;IACrE,CAAE;IAAAkC,QAAA,gBAEAjF,OAAA;MAAOkF,GAAG,EAAE/D,aAAc;MAACgE,GAAG,EAAC,mCAAmC;MAACC,OAAO,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpFxF,OAAA;MAAOkF,GAAG,EAAE9D,eAAgB;MAAC+D,GAAG,EAAC,iGAAiG;MAACC,OAAO,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEpJxF,OAAA;MAAK6E,SAAS,EAAC,mBAAmB;MAAAI,QAAA,gBAEhCjF,OAAA,CAACR,MAAM,CAACiG,GAAG;QACTZ,SAAS,EAAC,kBAAkB;QAC5Ba,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAX,QAAA,gBAE9BjF,OAAA;UAAI6E,SAAS,EAAC,yDAAyD;UAAAI,QAAA,EAAC;QAExE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxF,OAAA;UAAG6E,SAAS,EAAC,eAAe;UAAAI,QAAA,EAAC;QAE7B;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEbxF,OAAA;QAAK6E,SAAS,EAAC,uCAAuC;QAAAI,QAAA,gBAEpDjF,OAAA,CAACR,MAAM,CAACiG,GAAG;UACTZ,SAAS,EAAC,eAAe;UACzBa,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEG,KAAK,EAAE;UAAK,CAAE;UACrCD,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEG,KAAK,EAAE;UAAE,CAAE;UAAAb,QAAA,gBAElCjF,OAAA;YAAK6E,SAAS,EAAE,wCAAwC9B,WAAW,CAACnB,KAAK,oCAAoCmB,WAAW,CAACf,SAAS,8BAA+B;YAAAiD,QAAA,gBAE/JjF,OAAA;cAAK6E,SAAS,EAAC,6BAA6B;cAAAI,QAAA,eAC1CjF,OAAA;gBAAK6E,SAAS,EAAC,eAAe;gBAACC,KAAK,EAAE;kBACpCC,eAAe,EAAE;gBACnB;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eAENxF,OAAA;cAAK6E,SAAS,EAAC,eAAe;cAAAI,QAAA,gBAC5BjF,OAAA;gBAAK6E,SAAS,EAAC,wCAAwC;gBAAAI,QAAA,gBACrDjF,OAAA;kBAAAiF,QAAA,gBACEjF,OAAA;oBAAI6E,SAAS,EAAE,sBAAsB9B,WAAW,CAAClB,SAAS,OAAQ;oBAAAoD,QAAA,EAC/DlC,WAAW,CAACxB;kBAAI;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACLxF,OAAA;oBAAG6E,SAAS,EAAE,WAAW9B,WAAW,CAAClB,SAAS,aAAc;oBAAAoD,QAAA,EACzDlC,WAAW,CAACvB;kBAAK;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNxF,OAAA;kBAAK6E,SAAS,EAAC,UAAU;kBAAAI,QAAA,EACtBlC,WAAW,CAAChB;gBAAI;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxF,OAAA;gBAAG6E,SAAS,EAAE,GAAG9B,WAAW,CAAClB,SAAS,kCAAmC;gBAAAoD,QAAA,EACtElC,WAAW,CAACtB;cAAW;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eAEJxF,OAAA;gBAAK6E,SAAS,EAAC,6BAA6B;gBAAAI,QAAA,EACzClC,WAAW,CAACrB,eAAe,CAACqE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3CjG,OAAA,CAACR,MAAM,CAACiG,GAAG;kBAETZ,SAAS,EAAC,6BAA6B;kBACvCa,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEO,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCL,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEO,CAAC,EAAE;kBAAE,CAAE;kBAC9BC,UAAU,EAAE;oBAAEC,KAAK,EAAEH,KAAK,GAAG;kBAAI,CAAE;kBAAAhB,QAAA,gBAEnCjF,OAAA;oBAAK6E,SAAS,EAAC;kBAA4C;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9DxF,OAAA;oBAAM6E,SAAS,EAAE,WAAW9B,WAAW,CAAClB,SAAS,aAAc;oBAAAoD,QAAA,EAC5De;kBAAI;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA,GATFS,KAAK;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAUA,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENxF,OAAA;gBAAK6E,SAAS,EAAE,WAAW9B,WAAW,CAAClB,SAAS,aAAc;gBAAAoD,QAAA,GAAC,YACnD,EAAClC,WAAW,CAACpB,QAAQ;cAAA;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxF,OAAA,CAACR,MAAM,CAACiG,GAAG;YACTZ,SAAS,EAAC,4CAA4C;YACtDa,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BO,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAAAnB,QAAA,EAE1BoB,MAAM,CAACC,OAAO,CAACnG,UAAU,CAACwC,WAAW,CAAC,CAAC4D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACR,GAAG,CAAC,CAAC,CAACtB,MAAM,EAAEC,KAAK,CAAC,kBACtE1E,OAAA;cAAkB6E,SAAS,EAAC,gCAAgC;cAAAI,QAAA,gBAC1DjF,OAAA;gBAAK6E,SAAS,EAAC,eAAe;gBAAAI,QAAA,EAC3BT,kBAAkB,CAACC,MAAM,EAAEC,KAAK;cAAC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACNxF,OAAA;gBAAK6E,SAAS,EAAC,mDAAmD;gBAAAI,QAAA,EAC/DR;cAAM;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNxF,OAAA;gBAAK6E,SAAS,EAAC,oCAAoC;gBAAAI,QAAA,GAChDP,KAAK,EAAC,GACT;cAAA;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNxF,OAAA;gBAAK6E,SAAS,EAAC,4CAA4C;gBAAAI,QAAA,eACzDjF,OAAA;kBACE6E,SAAS,EAAE,kDACTJ,MAAM,KAAK,UAAU,GAAG,YAAY,GAAG,cAAc,EACpD;kBACHK,KAAK,EAAE;oBAAE0B,KAAK,EAAE,GAAG9B,KAAK;kBAAI;gBAAE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GAjBEf,MAAM;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkBX,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC,eAGbxF,OAAA,CAACR,MAAM,CAACiG,GAAG;YACTZ,SAAS,EAAC,MAAM;YAChBa,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BO,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAAAnB,QAAA,gBAE3BjF,OAAA;cAAI6E,SAAS,EAAC,8CAA8C;cAAAI,QAAA,EAAC;YAE7D;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxF,OAAA;cAAK6E,SAAS,EAAC,uCAAuC;cAAAI,QAAA,EACnD7C,SAAS,CAAC2D,GAAG,CAAE9C,QAAQ,iBACtBjD,OAAA,CAACR,MAAM,CAACiG,GAAG;gBAETZ,SAAS,EAAE,2DACT5B,QAAQ,CAACR,SAAS,GACd,mCAAmCM,WAAW,CAACf,SAAS,EAAE,GAC1D,+CAA+C,EAClD;gBACHyE,UAAU,EAAExD,QAAQ,CAACR,SAAS,GAAG;kBAAEmD,CAAC,EAAE,CAAC;gBAAE,CAAC,GAAG,CAAC,CAAE;gBAChDc,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAI,CAACzD,QAAQ,CAACR,SAAS,EAAE;kBACzB,IAAIQ,QAAQ,CAACP,OAAO,KAAK,SAAS,EAAE;oBAClC5B,cAAc,CAAC,IAAI,CAAC;kBACtB,CAAC,MAAM;oBACLJ,mBAAmB,CAACuC,QAAQ,CAACZ,EAAE,CAAC;kBAClC;gBACF,CAAE;gBAAA4C,QAAA,gBAEFjF,OAAA;kBAAK6E,SAAS,EAAC,6BAA6B;kBAAAI,QAAA,gBAC1CjF,OAAA;oBAAK6E,SAAS,EAAC,UAAU;oBAAAI,QAAA,EAAEhC,QAAQ,CAAClB;kBAAI;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/CxF,OAAA;oBAAK6E,SAAS,EAAC,QAAQ;oBAAAI,QAAA,gBACrBjF,OAAA;sBAAI6E,SAAS,EAAC,kCAAkC;sBAAAI,QAAA,EAC7ChC,QAAQ,CAAC1B;oBAAI;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ,CAAC,eACLxF,OAAA;sBAAG6E,SAAS,EAAC,4BAA4B;sBAAAI,QAAA,EACtChC,QAAQ,CAACxB;oBAAW;sBAAA4D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,eACJxF,OAAA;sBAAK6E,SAAS,EAAC,wBAAwB;sBAAAI,QAAA,gBACrCjF,OAAA;wBAAM6E,SAAS,EAAC,gBAAgB;wBAAAI,QAAA,GAAC,GAC9B,EAAChC,QAAQ,CAACX,QAAQ,CAACC,KAAK,EAAC,QAC5B;sBAAA;wBAAA8C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACPxF,OAAA;wBAAM6E,SAAS,EAAC,iBAAiB;wBAAAI,QAAA,GAAC,GAC/B,EAAChC,QAAQ,CAACX,QAAQ,CAACE,MAAM,EAAC,SAC7B;sBAAA;wBAAA6C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACL,CAACvC,QAAQ,CAACR,SAAS,iBAClBzC,OAAA;kBAAK6E,SAAS,EAAC,2BAA2B;kBAAAI,QAAA,EAAC;gBAE3C;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA,GAvCIvC,QAAQ,CAACZ,EAAE;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwCN,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGbxF,OAAA;UAAK6E,SAAS,EAAC,WAAW;UAAAI,QAAA,gBAExBjF,OAAA;YAAK6E,SAAS,EAAC,oBAAoB;YAAAI,QAAA,gBACjCjF,OAAA;cAAI6E,SAAS,EAAC,6CAA6C;cAAAI,QAAA,EAAC;YAE5D;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxF,OAAA;cAAK6E,SAAS,EAAC,WAAW;cAAAI,QAAA,EACvBoB,MAAM,CAACC,OAAO,CAACjF,gBAAgB,CAAC,CAAC0E,GAAG,CAAC,CAAC,CAACY,IAAI,EAAEC,IAAI,CAAC,kBACjD5G,OAAA;gBAEE0G,OAAO,EAAEA,CAAA,KAAM9C,oBAAoB,CAAC+C,IAAY,CAAE;gBAClDE,QAAQ,EAAElG,eAAgB;gBAC1BkE,SAAS,EAAE,yDACT1E,UAAU,CAAC4C,WAAW,KAAK4D,IAAI,GAC3B,kCAAkC,GAClC,0CAA0C,IAC5ChG,eAAe,GAAG,+BAA+B,GAAG,EAAE,EAAG;gBAAAsE,QAAA,eAE7DjF,OAAA;kBAAK6E,SAAS,EAAC,mBAAmB;kBAAAI,QAAA,gBAChCjF,OAAA;oBAAM6E,SAAS,EAAC,eAAe;oBAAAI,QAAA,EAAE2B,IAAI,CAAC7E;kBAAI;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAClDxF,OAAA;oBAAAiF,QAAA,gBACEjF,OAAA;sBAAK6E,SAAS,EAAC,2BAA2B;sBAAAI,QAAA,EAAE2B,IAAI,CAACrF;oBAAI;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5DxF,OAAA;sBAAK6E,SAAS,EAAC,uBAAuB;sBAAAI,QAAA,EAAE2B,IAAI,CAACpF;oBAAK;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAfDmB,IAAI;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBH,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNxF,OAAA;cAAK6E,SAAS,EAAC,wCAAwC;cAAAI,QAAA,gBACrDjF,OAAA;gBAAK6E,SAAS,EAAC,uBAAuB;gBAAAI,QAAA,EAAC;cAEvC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNxF,OAAA;gBACE0G,OAAO,EAAEA,CAAA,KAAM1F,kBAAkB,CAAC,IAAI,CAAE;gBACxC6D,SAAS,EAAC,6DAA6D;gBAAAI,QAAA,EACxE;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxF,OAAA;YAAK6E,SAAS,EAAC,oBAAoB;YAAAI,QAAA,gBACjCjF,OAAA;cAAI6E,SAAS,EAAC,6CAA6C;cAAAI,QAAA,EAAC;YAE5D;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxF,OAAA;cAAK6E,SAAS,EAAC,aAAa;cAAAI,QAAA,gBAC1BjF,OAAA;gBAAK6E,SAAS,EAAC,0CAA0C;gBAAAI,QAAA,EACtD9E,UAAU,CAAC2G;cAAe;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC,eACNxF,OAAA;gBAAK6E,SAAS,EAAC,4BAA4B;gBAAAI,QAAA,EAAC;cAE5C;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNxF,OAAA;gBAAK6E,SAAS,EAAC,qCAAqC;gBAAAI,QAAA,eAClDjF,OAAA;kBACE6E,SAAS,EAAE,gDACT1E,UAAU,CAAC2G,eAAe,IAAI,CAAC,GAAG,cAAc,GAAG,YAAY,EAC9D;kBACHhC,KAAK,EAAE;oBACL0B,KAAK,EAAE,GAAGO,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC9G,UAAU,CAAC2G,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC,CAAC;kBAChF;gBAAE;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxF,OAAA;YAAK6E,SAAS,EAAC,oBAAoB;YAAAI,QAAA,gBACjCjF,OAAA;cAAI6E,SAAS,EAAC,6CAA6C;cAAAI,QAAA,EAAC;YAE5D;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxF,OAAA;cAAK6E,SAAS,EAAC,WAAW;cAAAI,QAAA,gBACxBjF,OAAA;gBAAK6E,SAAS,EAAC,uDAAuD;gBAAAI,QAAA,gBACpEjF,OAAA,CAACL,GAAG;kBAACkF,SAAS,EAAC;gBAAuB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzCxF,OAAA;kBAAAiF,QAAA,gBACEjF,OAAA;oBAAK6E,SAAS,EAAC,qBAAqB;oBAAAI,QAAA,EAAC;kBAAa;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxDxF,OAAA;oBAAK6E,SAAS,EAAC,uBAAuB;oBAAAI,QAAA,EAAC;kBAA0B;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxF,OAAA;gBAAK6E,SAAS,EAAC,yDAAyD;gBAAAI,QAAA,gBACtEjF,OAAA,CAACJ,QAAQ;kBAACiF,SAAS,EAAC;gBAAyB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChDxF,OAAA;kBAAAiF,QAAA,gBACEjF,OAAA;oBAAK6E,SAAS,EAAC,qBAAqB;oBAAAI,QAAA,EAAC;kBAAe;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1DxF,OAAA;oBAAK6E,SAAS,EAAC,uBAAuB;oBAAAI,QAAA,EAAC;kBAA0B;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLpF,MAAM,iBACLJ,OAAA;YAAK6E,SAAS,EAAC,oBAAoB;YAAAI,QAAA,gBACjCjF,OAAA;cAAI6E,SAAS,EAAC,6CAA6C;cAAAI,QAAA,EAAC;YAE5D;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxF,OAAA;cAAK6E,SAAS,EAAC,WAAW;cAAAI,QAAA,gBACxBjF,OAAA;gBAAK6E,SAAS,EAAC,sBAAsB;gBAAAI,QAAA,gBACnCjF,OAAA;kBAAM6E,SAAS,EAAC,uBAAuB;kBAAAI,QAAA,EAAC;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChExF,OAAA;kBAAM6E,SAAS,EAAC,aAAa;kBAAAI,QAAA,EAC1B7E,MAAM,CAAC8G,KAAK,CAAC3E,KAAK,CAAC4E,MAAM,CAACC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7D,OAAO,CAACM,QAAQ,CAAC,aAAa,CAAC,CAAC,CAACwD;gBAAM;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNxF,OAAA;gBAAK6E,SAAS,EAAC,sBAAsB;gBAAAI,QAAA,gBACnCjF,OAAA;kBAAM6E,SAAS,EAAC,uBAAuB;kBAAAI,QAAA,EAAC;gBAAkB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjExF,OAAA;kBAAM6E,SAAS,EAAC,4BAA4B;kBAAAI,QAAA,GAAC,GAC1C,EAAC8B,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE7G,MAAM,CAAC8G,KAAK,CAAC3E,KAAK,CAACgF,KAAK,CAAC,EAAC,QAC1C;gBAAA;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNxF,OAAA;gBAAK6E,SAAS,EAAC,sBAAsB;gBAAAI,QAAA,gBACnCjF,OAAA;kBAAM6E,SAAS,EAAC,uBAAuB;kBAAAI,QAAA,EAAC;gBAAmB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAClExF,OAAA;kBAAM6E,SAAS,EAAC,aAAa;kBAAAI,QAAA,EAC1B7E,MAAM,CAAC8G,KAAK,CAACM,cAAc,GAAG,EAAE,GAAG,MAAM,GACzCpH,MAAM,CAAC8G,KAAK,CAACM,cAAc,GAAG,EAAE,GAAG,QAAQ,GAAG;gBAAS;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxF,OAAA,CAACP,eAAe;QAAAwF,QAAA,EACbxE,gBAAgB,iBACfT,OAAA,CAACR,MAAM,CAACiG,GAAG;UACTZ,SAAS,EAAC,qEAAqE;UAC/Ea,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxB8B,IAAI,EAAE;YAAE9B,OAAO,EAAE;UAAE,CAAE;UACrBe,OAAO,EAAEA,CAAA,KAAMhG,mBAAmB,CAAC,IAAI,CAAE;UAAAuE,QAAA,eAEzCjF,OAAA,CAACR,MAAM,CAACiG,GAAG;YACTZ,SAAS,EAAC,oCAAoC;YAC9Ca,OAAO,EAAE;cAAEI,KAAK,EAAE,GAAG;cAAEH,OAAO,EAAE;YAAE,CAAE;YACpCE,OAAO,EAAE;cAAEC,KAAK,EAAE,CAAC;cAAEH,OAAO,EAAE;YAAE,CAAE;YAClC8B,IAAI,EAAE;cAAE3B,KAAK,EAAE,GAAG;cAAEH,OAAO,EAAE;YAAE,CAAE;YACjCe,OAAO,EAAGgB,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;YAAA1C,QAAA,EAEnC,CAAC,MAAM;cACN,MAAMhC,QAAQ,GAAGb,SAAS,CAACwF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACxF,EAAE,KAAK5B,gBAAgB,CAAE;cAChE,oBACET,OAAA;gBAAK6E,SAAS,EAAC,aAAa;gBAAAI,QAAA,gBAC1BjF,OAAA;kBAAK6E,SAAS,EAAC,eAAe;kBAAAI,QAAA,EAAEhC,QAAQ,CAAClB;gBAAI;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpDxF,OAAA;kBAAI6E,SAAS,EAAC,0CAA0C;kBAAAI,QAAA,EACrDhC,QAAQ,CAAC1B;gBAAI;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACLxF,OAAA;kBAAG6E,SAAS,EAAC,oBAAoB;kBAAAI,QAAA,EAC9BhC,QAAQ,CAACxB;gBAAW;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACJxF,OAAA;kBAAK6E,SAAS,EAAC,oCAAoC;kBAAAI,QAAA,gBACjDjF,OAAA;oBAAK6E,SAAS,EAAC,aAAa;oBAAAI,QAAA,gBAC1BjF,OAAA;sBAAK6E,SAAS,EAAC,mCAAmC;sBAAAI,QAAA,GAAC,GAChD,EAAChC,QAAQ,CAACX,QAAQ,CAACC,KAAK;oBAAA;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtB,CAAC,eACNxF,OAAA;sBAAK6E,SAAS,EAAC,uBAAuB;sBAAAI,QAAA,EAAC;oBAAK;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC,eACNxF,OAAA;oBAAK6E,SAAS,EAAC,aAAa;oBAAAI,QAAA,gBAC1BjF,OAAA;sBAAK6E,SAAS,EAAC,oCAAoC;sBAAAI,QAAA,GAAC,GACjD,EAAChC,QAAQ,CAACX,QAAQ,CAACE,MAAM;oBAAA;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC,eACNxF,OAAA;sBAAK6E,SAAS,EAAC,uBAAuB;sBAAAI,QAAA,EAAC;oBAAM;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxF,OAAA;kBAAK6E,SAAS,EAAC,gBAAgB;kBAAAI,QAAA,gBAC7BjF,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAMhG,mBAAmB,CAAC,IAAI,CAAE;oBACzCmE,SAAS,EAAC,qEAAqE;oBAAAI,QAAA,EAChF;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTxF,OAAA;oBACE0G,OAAO,EAAEA,CAAA,KAAM1D,mBAAmB,CAACC,QAAQ,CAAE;oBAC7C4B,SAAS,EAAC,qBAAqB;oBAAAI,QAAA,EAChC;kBAED;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAEV,CAAC,EAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC,eAGlBxF,OAAA,CAACP,eAAe;QAAAwF,QAAA,EACbpE,WAAW,iBACVb,OAAA,CAACH,UAAU;UACTiI,OAAO,EAAEA,CAAA,KAAMhH,cAAc,CAAC,KAAK,CAAE;UACrCiH,OAAO,EAAEtE;QAAkB;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC,eAGlBxF,OAAA,CAACP,eAAe;QAAAwF,QAAA,EACblE,eAAe,iBACdf,OAAA,CAACF,WAAW;UACVgI,OAAO,EAAEA,CAAA,KAAM9G,kBAAkB,CAAC,KAAK,CAAE;UACzCgH,YAAY,EAAEpE;QAAqB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC,eAGlBxF,OAAA,CAACP,eAAe;QAAAwF,QAAA,EACbtE,eAAe,iBACdX,OAAA,CAACR,MAAM,CAACiG,GAAG;UACTZ,SAAS,EAAC,iEAAiE;UAC3Ea,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxB8B,IAAI,EAAE;YAAE9B,OAAO,EAAE;UAAE,CAAE;UAAAV,QAAA,eAErBjF,OAAA;YAAK6E,SAAS,EAAC,aAAa;YAAAI,QAAA,GAEzBhE,aAAa,KAAK,OAAO,iBACxBjB,OAAA,CAACR,MAAM,CAACiG,GAAG;cACTZ,SAAS,EAAC,eAAe;cACzBgB,OAAO,EAAE;gBAAEC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;gBAAEmC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;cAAE,CAAE;cACzD9B,UAAU,EAAE;gBAAExE,QAAQ,EAAE,CAAC;gBAAEuG,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAY,CAAE;cAAAnD,QAAA,EAClE;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACb,EACAvE,aAAa,KAAK,OAAO,iBACxBjB,OAAA,CAACR,MAAM,CAACiG,GAAG;cACTZ,SAAS,EAAC,eAAe;cACzBgB,OAAO,EAAE;gBAAEC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;gBAAEmC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;cAAE,CAAE;cACvD9B,UAAU,EAAE;gBAAExE,QAAQ,EAAE,GAAG;gBAAEuG,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAY,CAAE;cAAAnD,QAAA,EACpE;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACb,EACAvE,aAAa,KAAK,SAAS,iBAC1BjB,OAAA,CAACR,MAAM,CAACiG,GAAG;cACTZ,SAAS,EAAC,eAAe;cACzBgB,OAAO,EAAE;gBAAEC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;gBAAEmC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;cAAE,CAAE;cACxD9B,UAAU,EAAE;gBAAExE,QAAQ,EAAE,GAAG;gBAAEuG,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAY,CAAE;cAAAnD,QAAA,EACpE;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACb,EACAvE,aAAa,KAAK,MAAM,iBACvBjB,OAAA,CAACR,MAAM,CAACiG,GAAG;cACTZ,SAAS,EAAC,eAAe;cACzBgB,OAAO,EAAE;gBAAEC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;gBAAEmC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;cAAE,CAAE;cAC1D9B,UAAU,EAAE;gBAAExE,QAAQ,EAAE,CAAC;gBAAEuG,MAAM,EAAEC,QAAQ;gBAAEC,IAAI,EAAE;cAAY,CAAE;cAAAnD,QAAA,EAClE;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CACb,eACDxF,OAAA;cAAI6E,SAAS,EAAC,0CAA0C;cAAAI,QAAA,EAAC;YAEzD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxF,OAAA;cAAG6E,SAAS,EAAC,eAAe;cAAAI,QAAA,EAAC;YAE7B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MACb;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtF,EAAA,CAnnBID,aAAuB;EAAA,QACiEP,YAAY;AAAA;AAAA2I,EAAA,GADpGpI,aAAuB;AAqnB7B,eAAeA,aAAa;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}