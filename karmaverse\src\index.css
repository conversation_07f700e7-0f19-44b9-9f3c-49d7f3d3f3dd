@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=Noto+Sans+Devanagari:wght@100;200;300;400;500;600;700;800;900&display=swap');

@layer base {
  body {
    @apply bg-gradient-to-br from-sacred-50 via-lotus-50 to-saffron-50;
    @apply font-spiritual text-gray-800;
    @apply min-h-screen;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .spiritual-card {
    @apply bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-saffron-200/50;
    @apply hover:shadow-xl transition-all duration-300;
  }
  
  .karma-button {
    @apply bg-gradient-to-r from-saffron-500 to-saffron-600;
    @apply text-white font-semibold py-3 px-6 rounded-lg;
    @apply hover:from-saffron-600 hover:to-saffron-700;
    @apply transform hover:scale-105 transition-all duration-200;
    @apply shadow-lg hover:shadow-xl;
  }
  
  .chakra-glow {
    @apply animate-glow;
    filter: drop-shadow(0 0 10px rgba(237, 118, 17, 0.6));
  }
  
  .yuga-transition {
    @apply transition-all duration-1000 ease-in-out;
  }
  
  .sanskrit-text {
    @apply font-sanskrit text-saffron-700 font-medium;
  }
  
  .spiritual-gradient {
    background: linear-gradient(135deg, 
      rgba(237, 118, 17, 0.1) 0%, 
      rgba(236, 72, 153, 0.1) 50%, 
      rgba(14, 165, 233, 0.1) 100%);
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-saffron-50;
}

::-webkit-scrollbar-thumb {
  @apply bg-saffron-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-saffron-400;
}

/* Meditation breathing animation */
@keyframes breathe {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.breathe {
  animation: breathe 4s ease-in-out infinite;
}

/* Om symbol animation */
@keyframes om-pulse {
  0%, 100% { 
    transform: scale(1) rotate(0deg);
    opacity: 0.8;
  }
  50% { 
    transform: scale(1.2) rotate(180deg);
    opacity: 1;
  }
}

.om-pulse {
  animation: om-pulse 6s ease-in-out infinite;
}