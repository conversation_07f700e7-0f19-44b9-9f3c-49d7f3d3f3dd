# 🕉️ KarmaVerse - Interactive Hindu Philosophy & Spiritual Journey Simulator

> *"As a person puts on new garments, giving up old ones, the soul similarly accepts new material bodies, giving up the old and useless ones."* - Bhagavad Gita 2.22

KarmaVerse is a profound, interactive React application that transforms ancient Hindu wisdom into an engaging digital experience. Journey through the cosmic cycles, make dharmic choices, and evolve spiritually through the timeless teachings of the Bhagavad Gita, Upanishads, and Vedantic philosophy.

## ✨ Features

### 🧘‍♂️ **Spiritual Avatar System**
- Create your unique spiritual avatar with customizable appearance
- Choose from four classical yoga paths: <PERSON>rma, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, or Raja
- Watch your avatar evolve through reincarnations based on karma
- Beautiful aura effects that reflect your spiritual progress

### ⚖️ **Karma & Dharma Engine**
- Real-time karma tracking with positive and negative actions
- Daily dharma dilemmas based on real-life moral scenarios
- Scripture-backed consequences for every choice
- Detailed karma history and pattern analysis

### 🌌 **Three Gunas Balance**
- Dynamic tracking of Satt<PERSON> (purity), <PERSON><PERSON> (passion), and <PERSON><PERSON> (inertia)
- Visual representation of your spiritual qualities
- Guna-based character development and abilities
- Personalized guidance based on dominant guna

### 🏛️ **Yuga Progression System**
- Experience all four cosmic ages: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and Kali Yuga
- World environment changes based on collective karma
- Unique challenges and opportunities in each yuga
- Beautiful visual transitions reflecting cosmic cycles

### 📚 **Living Scripture Integration**
- Daily wisdom from Bhagavad Gita and Upanishads
- Context-aware scripture quotes for life situations
- Sanskrit verses with accurate translations
- AI-powered spiritual guidance system

### 🧠 **Meditation & Mindfulness**
- Interactive meditation sessions with breathing guides
- Chakra visualization and energy flow animations
- Mantra chanting with sacred sound frequencies
- Progress tracking for spiritual practices

### 📖 **Spiritual Journal**
- Reflective journaling with guided prompts
- Mood tracking and insight recording
- Integration with karma actions and spiritual progress
- Beautiful, printable journal entries

## 🛠️ Technology Stack

- **Frontend**: React 18 with TypeScript
- **Styling**: Tailwind CSS with custom Hindu-inspired themes
- **Animations**: Framer Motion for smooth, spiritual animations
- **State Management**: Zustand with persistence
- **3D Graphics**: Three.js for chakra and aura visualizations
- **AI Integration**: Multiple free APIs (OpenRouter, Hugging Face, Groq)
- **Charts**: Chart.js for progress visualization
- **Icons**: Lucide React for beautiful iconography

## 🚀 Getting Started

### Prerequisites
- Node.js 16+ 
- npm or yarn
- Modern web browser

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/karmaverse.git
   cd karmaverse
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables** (Optional for AI features)
   ```bash
   # Create .env.local file
   REACT_APP_AI_API_KEY=your_api_key_here
   ```

4. **Start the development server**
   ```bash
   npm start
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000` and begin your spiritual journey!

## 🎮 How to Play

### 1. **Create Your Avatar**
- Choose a spiritual name that resonates with you
- Select your yoga path based on your spiritual inclination
- Watch your avatar come to life with beautiful animations

### 2. **Daily Spiritual Practice**
- Start each day with wisdom from sacred texts
- Face dharma dilemmas that test your moral compass
- Make choices that shape your karma and spiritual evolution

### 3. **Track Your Progress**
- Monitor your karma balance and guna distribution
- Watch your avatar evolve through different forms
- Unlock new spiritual abilities and insights

### 4. **Explore the World**
- Experience different yugas and their unique challenges
- Participate in collective karma that affects the world state
- Discover hidden wisdom and spiritual treasures

## 🎨 Spiritual Design Philosophy

KarmaVerse embraces authentic Hindu aesthetics with:
- **Saffron, Lotus, and Sacred color palettes** inspired by temple art
- **Sanskrit typography** for mantras and sacred texts
- **Sacred geometry patterns** in backgrounds and animations
- **Chakra-inspired visual effects** and energy flows
- **Respectful iconography** honoring Hindu traditions

## 🌟 Key Spiritual Concepts

### **Dharma** (Righteous Duty)
Every choice in KarmaVerse is evaluated against dharmic principles, teaching players to consider duty, righteousness, and moral law in decision-making.

### **Karma** (Action & Consequence)
The game's core mechanic reflects the Hindu understanding that every action has consequences, binding the soul to the cycle of rebirth until liberation is achieved.

### **Samsara** (Cycle of Rebirth)
Players experience multiple lifetimes, with each rebirth influenced by accumulated karma, illustrating the journey toward moksha (liberation).

### **Moksha** (Liberation)
The ultimate goal is spiritual liberation through the transcendence of desire, attachment, and the ego, achieved through various yoga paths.

## 🤝 Contributing

We welcome contributions that honor the spiritual integrity of the project:

1. **Fork the repository**
2. **Create a feature branch** (`git checkout -b feature/spiritual-enhancement`)
3. **Commit your changes** (`git commit -m 'Add profound spiritual feature'`)
4. **Push to the branch** (`git push origin feature/spiritual-enhancement`)
5. **Open a Pull Request**

### Contribution Guidelines
- Maintain respect for Hindu philosophy and traditions
- Ensure accuracy in spiritual concepts and Sanskrit translations
- Follow the established design patterns and color schemes
- Include appropriate tests for new features
- Document any new spiritual concepts or mechanics

## 📜 Scripture Sources

All spiritual content is sourced from authentic Hindu scriptures:
- **Bhagavad Gita** - Various authentic translations
- **Upanishads** - Classical texts including Isha, Katha, and Mundaka
- **Yoga Sutras of Patanjali** - For meditation and raja yoga content
- **Mahabharata** - For dharmic stories and moral guidance

## 🙏 Acknowledgments

- **Ancient Rishis and Sages** for preserving eternal wisdom
- **Bhagavad Gita and Upanishads** for spiritual guidance
- **Hindu philosophical traditions** for profound insights
- **Open source community** for technical foundations
- **Beta testers** who provided valuable spiritual feedback

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🕉️ Spiritual Disclaimer

KarmaVerse is created with deep respect for Hindu philosophy and traditions. It aims to make ancient wisdom accessible through modern technology while maintaining the authenticity and profundity of the original teachings. This is a tool for spiritual exploration and learning, not a replacement for traditional study and practice.

---

*"Tat tvam asi"* - Thou art That

**May this digital ashram serve your spiritual journey and bring you closer to the eternal truth within.**

🌸 **Om Shanti Shanti Shanti** 🌸