{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Master1\\\\karmaverse\\\\src\\\\components\\\\Meditation\\\\MeditationCenter.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { useGameStore } from '../../store/gameStore';\nimport { audioService } from '../../services/audioService';\nimport { Play, Pause, RotateCcw, Volume2, VolumeX } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MeditationCenter = () => {\n  _s();\n  var _meditationTypes$find;\n  const {\n    avatar,\n    completeMeditation,\n    updateGunas,\n    addNotification\n  } = useGameStore();\n  const [isActive, setIsActive] = useState(false);\n  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes default\n  const [selectedDuration, setSelectedDuration] = useState(5);\n  const [meditationType, setMeditationType] = useState('breathing');\n  const [soundEnabled, setSoundEnabled] = useState(true);\n  const [breathPhase, setBreathPhase] = useState('inhale');\n  const audioRef = useRef(null);\n  const meditationTypes = React.useMemo(() => [{\n    type: 'breathing',\n    title: 'Pranayama',\n    description: 'Breath control meditation for inner peace',\n    icon: '🌬️',\n    benefits: {\n      sattva: 8,\n      focus: 10,\n      peace: 12\n    }\n  }, {\n    type: 'mantra',\n    title: 'Mantra Japa',\n    description: 'Sacred sound repetition for divine connection',\n    icon: '🕉️',\n    benefits: {\n      sattva: 12,\n      focus: 8,\n      peace: 10\n    }\n  }, {\n    type: 'visualization',\n    title: 'Dhyana',\n    description: 'Visualization meditation for clarity',\n    icon: '👁️',\n    benefits: {\n      sattva: 10,\n      focus: 12,\n      peace: 8\n    }\n  }, {\n    type: 'mindfulness',\n    title: 'Vipassana',\n    description: 'Mindful awareness of present moment',\n    icon: '🧘‍♂️',\n    benefits: {\n      sattva: 9,\n      focus: 11,\n      peace: 10\n    }\n  }], []);\n  const durations = React.useMemo(() => [{\n    minutes: 3,\n    label: '3 min',\n    difficulty: 1\n  }, {\n    minutes: 5,\n    label: '5 min',\n    difficulty: 2\n  }, {\n    minutes: 10,\n    label: '10 min',\n    difficulty: 3\n  }, {\n    minutes: 15,\n    label: '15 min',\n    difficulty: 4\n  }, {\n    minutes: 20,\n    label: '20 min',\n    difficulty: 5\n  }, {\n    minutes: 30,\n    label: '30 min',\n    difficulty: 6\n  }], []);\n\n  // Breathing cycle timing (4-7-8 technique)\n  const breathingCycle = React.useMemo(() => ({\n    inhale: 4000,\n    hold: 7000,\n    exhale: 8000,\n    pause: 1000\n  }), []);\n\n  // Initialize audio service\n  useEffect(() => {\n    // Set audio service to enabled based on sound setting\n    audioService.setEnabled(soundEnabled);\n    return () => {\n      // Stop any playing meditation music when component unmounts\n      audioService.stopAll();\n    };\n  }, [soundEnabled]);\n\n  // Move handleMeditationComplete above this useEffect to avoid TS2448 error\n  const handleMeditationComplete = React.useCallback(() => {\n    if (!avatar) return;\n    const selectedType = meditationTypes.find(t => t.type === meditationType);\n    const duration = durations.find(d => d.minutes === selectedDuration);\n    const session = {\n      id: `meditation_${Date.now()}`,\n      type: meditationType,\n      duration: selectedDuration,\n      difficulty: duration.difficulty,\n      rewards: selectedType.benefits\n    };\n    completeMeditation(session);\n    updateGunas({\n      sattva: selectedType.benefits.sattva\n    });\n    addNotification(`Meditation completed! +${selectedType.benefits.sattva} Sattva gained`);\n    setIsActive(false);\n    setTimeLeft(selectedDuration * 60);\n\n    // Stop meditation music when complete\n    audioService.stopAll();\n  }, [avatar, meditationTypes, meditationType, durations, selectedDuration, completeMeditation, updateGunas, addNotification]);\n  useEffect(() => {\n    let interval;\n    if (isActive && timeLeft > 0) {\n      interval = setInterval(() => {\n        setTimeLeft(timeLeft - 1);\n      }, 1000);\n    } else if (timeLeft === 0) {\n      handleMeditationComplete();\n    }\n    return () => clearInterval(interval);\n  }, [isActive, timeLeft, handleMeditationComplete]);\n\n  // Breathing animation cycle\n  useEffect(() => {\n    if (isActive && meditationType === 'breathing') {\n      const cycleBreathing = () => {\n        setBreathPhase('inhale');\n        setTimeout(() => setBreathPhase('hold'), breathingCycle.inhale);\n        setTimeout(() => setBreathPhase('exhale'), breathingCycle.inhale + breathingCycle.hold);\n        setTimeout(() => setBreathPhase('pause'), breathingCycle.inhale + breathingCycle.hold + breathingCycle.exhale);\n      };\n      cycleBreathing();\n      const breathInterval = setInterval(cycleBreathing, Object.values(breathingCycle).reduce((a, b) => a + b, 0));\n      return () => clearInterval(breathInterval);\n    }\n  }, [isActive, meditationType, breathingCycle]);\n  const startMeditation = async () => {\n    setTimeLeft(selectedDuration * 60);\n    setIsActive(true);\n\n    // Play meditation music if sound is enabled\n    if (soundEnabled) {\n      try {\n        await audioService.playMeditationMusic();\n        console.log('Meditation music started successfully');\n      } catch (error) {\n        console.error('Failed to start meditation music:', error);\n      }\n    }\n  };\n  const pauseMeditation = () => {\n    setIsActive(!isActive);\n\n    // Pause or resume audio based on meditation state\n    if (isActive) {\n      audioService.pauseCurrent();\n    } else if (soundEnabled) {\n      audioService.resumeCurrent();\n    }\n  };\n  const resetMeditation = () => {\n    setIsActive(false);\n    setTimeLeft(selectedDuration * 60);\n    setBreathPhase('inhale');\n\n    // Stop meditation music when reset\n    audioService.stopAll();\n  };\n\n  // ...existing code...\n  // (Removed duplicate handleMeditationComplete declaration)\n  // ...existing code...\n\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n  const getBreathingInstruction = () => {\n    switch (breathPhase) {\n      case 'inhale':\n        return 'Breathe In...';\n      case 'hold':\n        return 'Hold...';\n      case 'exhale':\n        return 'Breathe Out...';\n      case 'pause':\n        return 'Pause...';\n      default:\n        return 'Breathe...';\n    }\n  };\n  const getChakraColor = index => {\n    const colors = ['#ff0000', '#ff8c00', '#ffff00', '#00ff00', '#0000ff', '#4b0082', '#9400d3'];\n    return colors[index % colors.length];\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen pt-20 p-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"text-center mb-8\",\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-spiritual font-bold text-saffron-700 mb-2\",\n          children: \"Meditation Center\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Find inner peace through ancient practices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"lg:col-span-1 space-y-6\",\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spiritual-card p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-4 text-saffron-700\",\n              children: \"Choose Practice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: meditationTypes.map(type => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setMeditationType(type.type),\n                disabled: isActive,\n                className: `w-full text-left p-3 rounded-lg border transition-all ${meditationType === type.type ? 'border-saffron-300 bg-saffron-50' : 'border-gray-200 hover:border-saffron-200'} ${isActive ? 'opacity-50 cursor-not-allowed' : ''}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl\",\n                    children: type.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-800\",\n                      children: type.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-600\",\n                      children: type.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this)\n              }, type.type, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spiritual-card p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-4 text-saffron-700\",\n              children: \"Duration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-2\",\n              children: durations.map(duration => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedDuration(duration.minutes),\n                disabled: isActive,\n                className: `p-3 rounded-lg border text-center transition-all ${selectedDuration === duration.minutes ? 'border-saffron-300 bg-saffron-50 text-saffron-700' : 'border-gray-200 hover:border-saffron-200'} ${isActive ? 'opacity-50 cursor-not-allowed' : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium\",\n                  children: duration.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"Level \", duration.difficulty]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this)]\n              }, duration.minutes, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spiritual-card p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center space-x-4\",\n              children: [!isActive ? /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: startMeditation,\n                className: \"karma-button flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Play, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Begin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: pauseMeditation,\n                className: \"karma-button flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Pause, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Pause\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: resetMeditation,\n                className: \"px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(RotateCcw, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Reset\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  const newSoundState = !soundEnabled;\n                  setSoundEnabled(newSoundState);\n\n                  // Handle audio based on new sound state\n                  if (audioRef.current) {\n                    if (newSoundState && isActive) {\n                      audioRef.current.play().catch(e => console.log('Audio play failed:', e));\n                    } else {\n                      audioRef.current.pause();\n                    }\n                  }\n                },\n                className: \"p-2 border border-gray-300 rounded-lg hover:bg-gray-50\",\n                children: soundEnabled ? /*#__PURE__*/_jsxDEV(Volume2, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 35\n                }, this) : /*#__PURE__*/_jsxDEV(VolumeX, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 69\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"lg:col-span-2\",\n          initial: {\n            opacity: 0,\n            scale: 0.9\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spiritual-card p-8 h-full flex flex-col items-center justify-center min-h-[500px]\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"text-6xl font-mono font-bold text-saffron-700 mb-8\",\n              animate: {\n                scale: isActive ? [1, 1.05, 1] : 1\n              },\n              transition: {\n                duration: 1,\n                repeat: isActive ? Infinity : 0\n              },\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative w-80 h-80 flex items-center justify-center\",\n              children: [meditationType === 'breathing' && /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute w-64 h-64 rounded-full border-4 border-saffron-300 flex items-center justify-center\",\n                animate: {\n                  scale: breathPhase === 'inhale' ? 1.3 : breathPhase === 'exhale' ? 0.7 : 1,\n                  borderColor: breathPhase === 'inhale' ? '#10b981' : breathPhase === 'exhale' ? '#3b82f6' : '#f59e0b'\n                },\n                transition: {\n                  duration: breathPhase === 'inhale' ? 4 : breathPhase === 'exhale' ? 8 : 1\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl mb-2\",\n                    children: \"\\uD83C\\uDF2C\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-lg font-medium text-gray-700\",\n                    children: getBreathingInstruction()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this), meditationType === 'mantra' && /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"text-center\",\n                animate: {\n                  scale: [1, 1.1, 1]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity\n                },\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"text-8xl mb-4\",\n                  animate: {\n                    rotate: 360\n                  },\n                  transition: {\n                    duration: 20,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                  },\n                  children: \"\\uD83D\\uDD49\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 368,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"sanskrit-text text-2xl text-saffron-700\",\n                  children: \"\\u0950 \\u092E\\u0923\\u093F \\u092A\\u0926\\u094D\\u092E\\u0947 \\u0939\\u0942\\u0901\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 mt-2\",\n                  children: \"Om Mani Padme Hum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this), meditationType === 'visualization' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [[...Array(7)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute w-12 h-12 rounded-full\",\n                  style: {\n                    backgroundColor: getChakraColor(i),\n                    top: `${i * 40}px`,\n                    left: '50%',\n                    transform: 'translateX(-50%)'\n                  },\n                  animate: {\n                    scale: [1, 1.2, 1],\n                    opacity: [0.7, 1, 0.7]\n                  },\n                  transition: {\n                    duration: 2,\n                    repeat: Infinity,\n                    delay: i * 0.3\n                  }\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 23\n                }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mt-80\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-lg font-medium text-gray-700\",\n                    children: \"Visualize energy flowing through chakras\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 386,\n                columnNumber: 19\n              }, this), meditationType === 'mindfulness' && /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"text-center\",\n                animate: {\n                  opacity: [0.5, 1, 0.5]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl mb-4\",\n                  children: \"\\uD83E\\uDDD8\\u200D\\u2642\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-lg font-medium text-gray-700 mb-2\",\n                  children: \"Be present in this moment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Observe thoughts without judgment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"absolute inset-0 w-full h-full -rotate-90\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  cx: \"50%\",\n                  cy: \"50%\",\n                  r: \"150\",\n                  fill: \"none\",\n                  stroke: \"#f3f4f6\",\n                  strokeWidth: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.circle, {\n                  cx: \"50%\",\n                  cy: \"50%\",\n                  r: \"150\",\n                  fill: \"none\",\n                  stroke: \"#f59e0b\",\n                  strokeWidth: \"4\",\n                  strokeLinecap: \"round\",\n                  strokeDasharray: `${2 * Math.PI * 150}`,\n                  strokeDashoffset: `${2 * Math.PI * 150 * (timeLeft / (selectedDuration * 60))}`,\n                  transition: {\n                    duration: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this), !isActive && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"mt-8 text-center\",\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              transition: {\n                delay: 0.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 mb-2\",\n                children: \"Benefits of this practice:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-center space-x-4\",\n                children: Object.entries(((_meditationTypes$find = meditationTypes.find(t => t.type === meditationType)) === null || _meditationTypes$find === void 0 ? void 0 : _meditationTypes$find.benefits) || {}).map(([benefit, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-lg font-bold text-saffron-600\",\n                    children: [\"+\", value]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500 capitalize\",\n                    children: benefit\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 471,\n                    columnNumber: 25\n                  }, this)]\n                }, benefit, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_s(MeditationCenter, \"ZmCCFnSatb0aHaQzcyvsS5+GctI=\", false, function () {\n  return [useGameStore];\n});\n_c = MeditationCenter;\nexport default MeditationCenter;\nvar _c;\n$RefreshReg$(_c, \"MeditationCenter\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "useGameStore", "audioService", "Play", "Pause", "RotateCcw", "Volume2", "VolumeX", "jsxDEV", "_jsxDEV", "MeditationCenter", "_s", "_meditationTypes$find", "avatar", "completeMeditation", "updateGunas", "addNotification", "isActive", "setIsActive", "timeLeft", "setTimeLeft", "selectedDuration", "setSelectedDuration", "meditationType", "setMeditationType", "soundEnabled", "setSoundEnabled", "breathPhase", "setBreathPhase", "audioRef", "meditationTypes", "useMemo", "type", "title", "description", "icon", "benefits", "sattva", "focus", "peace", "durations", "minutes", "label", "difficulty", "breathingCycle", "inhale", "hold", "exhale", "pause", "setEnabled", "stopAll", "handleMeditationComplete", "useCallback", "selectedType", "find", "t", "duration", "d", "session", "id", "Date", "now", "rewards", "interval", "setInterval", "clearInterval", "cycleBreathing", "setTimeout", "breathInterval", "Object", "values", "reduce", "a", "b", "startMeditation", "playMeditationMusic", "console", "log", "error", "pauseMeditation", "pauseCurrent", "resumeCurrent", "resetMeditation", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "getBreathingInstruction", "getChakraColor", "index", "colors", "length", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x", "map", "onClick", "disabled", "newSoundState", "current", "play", "catch", "e", "scale", "transition", "repeat", "Infinity", "borderColor", "rotate", "ease", "Array", "_", "i", "style", "backgroundColor", "top", "left", "transform", "delay", "cx", "cy", "r", "fill", "stroke", "strokeWidth", "circle", "strokeLinecap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PI", "strokeDashoffset", "entries", "benefit", "value", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Master1/karmaverse/src/components/Meditation/MeditationCenter.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { useGameStore } from '../../store/gameStore';\nimport { MeditationSession } from '../../types';\nimport { audioService } from '../../services/audioService';\nimport { Play, Pause, RotateCcw, Volume2, VolumeX } from 'lucide-react';\n\nconst MeditationCenter: React.FC = () => {\n  const { avatar, completeMeditation, updateGunas, addNotification } = useGameStore();\n  const [isActive, setIsActive] = useState(false);\n  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes default\n  const [selectedDuration, setSelectedDuration] = useState(5);\n  const [meditationType, setMeditationType] = useState<'breathing' | 'mantra' | 'visualization' | 'mindfulness'>('breathing');\n  const [soundEnabled, setSoundEnabled] = useState(true);\n  const [breathPhase, setBreathPhase] = useState<'inhale' | 'hold' | 'exhale' | 'pause'>('inhale');\n  const audioRef = useRef<HTMLAudioElement | null>(null);\n\n  const meditationTypes = React.useMemo(() => ([\n    {\n      type: 'breathing' as const,\n      title: 'Pranayama',\n      description: 'Breath control meditation for inner peace',\n      icon: '🌬️',\n      benefits: { sattva: 8, focus: 10, peace: 12 }\n    },\n    {\n      type: 'mantra' as const,\n      title: 'Mantra Japa',\n      description: 'Sacred sound repetition for divine connection',\n      icon: '🕉️',\n      benefits: { sattva: 12, focus: 8, peace: 10 }\n    },\n    {\n      type: 'visualization' as const,\n      title: 'Dhyana',\n      description: 'Visualization meditation for clarity',\n      icon: '👁️',\n      benefits: { sattva: 10, focus: 12, peace: 8 }\n    },\n    {\n      type: 'mindfulness' as const,\n      title: 'Vipassana',\n      description: 'Mindful awareness of present moment',\n      icon: '🧘‍♂️',\n      benefits: { sattva: 9, focus: 11, peace: 10 }\n    }\n  ]), []);\n\n  const durations = React.useMemo(() => ([\n    { minutes: 3, label: '3 min', difficulty: 1 },\n    { minutes: 5, label: '5 min', difficulty: 2 },\n    { minutes: 10, label: '10 min', difficulty: 3 },\n    { minutes: 15, label: '15 min', difficulty: 4 },\n    { minutes: 20, label: '20 min', difficulty: 5 },\n    { minutes: 30, label: '30 min', difficulty: 6 }\n  ]), []);\n\n  // Breathing cycle timing (4-7-8 technique)\n  const breathingCycle = React.useMemo(() => ({\n    inhale: 4000,\n    hold: 7000,\n    exhale: 8000,\n    pause: 1000\n  }), []);\n\n  // Initialize audio service\n  useEffect(() => {\n    // Set audio service to enabled based on sound setting\n    audioService.setEnabled(soundEnabled);\n\n    return () => {\n      // Stop any playing meditation music when component unmounts\n      audioService.stopAll();\n    };\n  }, [soundEnabled]);\n\n  // Move handleMeditationComplete above this useEffect to avoid TS2448 error\n  const handleMeditationComplete = React.useCallback(() => {\n    if (!avatar) return;\n\n    const selectedType = meditationTypes.find(t => t.type === meditationType)!;\n    const duration = durations.find(d => d.minutes === selectedDuration)!;\n\n    const session: MeditationSession = {\n      id: `meditation_${Date.now()}`,\n      type: meditationType,\n      duration: selectedDuration,\n      difficulty: duration.difficulty,\n      rewards: selectedType.benefits\n    };\n\n    completeMeditation(session);\n    updateGunas({ sattva: selectedType.benefits.sattva });\n    addNotification(`Meditation completed! +${selectedType.benefits.sattva} Sattva gained`);\n    \n    setIsActive(false);\n    setTimeLeft(selectedDuration * 60);\n\n    // Stop meditation music when complete\n    audioService.stopAll();\n  }, [avatar, meditationTypes, meditationType, durations, selectedDuration, completeMeditation, updateGunas, addNotification]);\n\n  useEffect(() => {\n    let interval: NodeJS.Timeout;\n    \n    if (isActive && timeLeft > 0) {\n      interval = setInterval(() => {\n        setTimeLeft(timeLeft - 1);\n      }, 1000);\n    } else if (timeLeft === 0) {\n      handleMeditationComplete();\n    }\n\n    return () => clearInterval(interval);\n  }, [isActive, timeLeft, handleMeditationComplete]);\n\n  // Breathing animation cycle\n  useEffect(() => {\n    if (isActive && meditationType === 'breathing') {\n      const cycleBreathing = () => {\n        setBreathPhase('inhale');\n        setTimeout(() => setBreathPhase('hold'), breathingCycle.inhale);\n        setTimeout(() => setBreathPhase('exhale'), breathingCycle.inhale + breathingCycle.hold);\n        setTimeout(() => setBreathPhase('pause'), breathingCycle.inhale + breathingCycle.hold + breathingCycle.exhale);\n      };\n\n      cycleBreathing();\n      const breathInterval = setInterval(cycleBreathing, Object.values(breathingCycle).reduce((a, b) => a + b, 0));\n      \n      return () => clearInterval(breathInterval);\n    }\n  }, [isActive, meditationType, breathingCycle]);\n\n  const startMeditation = async () => {\n    setTimeLeft(selectedDuration * 60);\n    setIsActive(true);\n\n    // Play meditation music if sound is enabled\n    if (soundEnabled) {\n      try {\n        await audioService.playMeditationMusic();\n        console.log('Meditation music started successfully');\n      } catch (error) {\n        console.error('Failed to start meditation music:', error);\n      }\n    }\n  };\n\n  const pauseMeditation = () => {\n    setIsActive(!isActive);\n\n    // Pause or resume audio based on meditation state\n    if (isActive) {\n      audioService.pauseCurrent();\n    } else if (soundEnabled) {\n      audioService.resumeCurrent();\n    }\n  };\n\n  const resetMeditation = () => {\n    setIsActive(false);\n    setTimeLeft(selectedDuration * 60);\n    setBreathPhase('inhale');\n\n    // Stop meditation music when reset\n    audioService.stopAll();\n  };\n\n  // ...existing code...\n  // (Removed duplicate handleMeditationComplete declaration)\n  // ...existing code...\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getBreathingInstruction = () => {\n    switch (breathPhase) {\n      case 'inhale': return 'Breathe In...';\n      case 'hold': return 'Hold...';\n      case 'exhale': return 'Breathe Out...';\n      case 'pause': return 'Pause...';\n      default: return 'Breathe...';\n    }\n  };\n\n  const getChakraColor = (index: number) => {\n    const colors = ['#ff0000', '#ff8c00', '#ffff00', '#00ff00', '#0000ff', '#4b0082', '#9400d3'];\n    return colors[index % colors.length];\n  };\n\n  return (\n    <div className=\"min-h-screen pt-20 p-6\">\n      <div className=\"max-w-4xl mx-auto\">\n        {/* Header */}\n        <motion.div\n          className=\"text-center mb-8\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n        >\n          <h1 className=\"text-4xl font-spiritual font-bold text-saffron-700 mb-2\">\n            Meditation Center\n          </h1>\n          <p className=\"text-gray-600\">\n            Find inner peace through ancient practices\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Meditation Setup */}\n          <motion.div\n            className=\"lg:col-span-1 space-y-6\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n          >\n            {/* Meditation Type Selection */}\n            <div className=\"spiritual-card p-6\">\n              <h3 className=\"text-lg font-semibold mb-4 text-saffron-700\">\n                Choose Practice\n              </h3>\n              <div className=\"space-y-3\">\n                {meditationTypes.map((type) => (\n                  <button\n                    key={type.type}\n                    onClick={() => setMeditationType(type.type)}\n                    disabled={isActive}\n                    className={`w-full text-left p-3 rounded-lg border transition-all ${\n                      meditationType === type.type\n                        ? 'border-saffron-300 bg-saffron-50'\n                        : 'border-gray-200 hover:border-saffron-200'\n                    } ${isActive ? 'opacity-50 cursor-not-allowed' : ''}`}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-2xl\">{type.icon}</span>\n                      <div>\n                        <div className=\"font-medium text-gray-800\">{type.title}</div>\n                        <div className=\"text-sm text-gray-600\">{type.description}</div>\n                      </div>\n                    </div>\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Duration Selection */}\n            <div className=\"spiritual-card p-6\">\n              <h3 className=\"text-lg font-semibold mb-4 text-saffron-700\">\n                Duration\n              </h3>\n              <div className=\"grid grid-cols-2 gap-2\">\n                {durations.map((duration) => (\n                  <button\n                    key={duration.minutes}\n                    onClick={() => setSelectedDuration(duration.minutes)}\n                    disabled={isActive}\n                    className={`p-3 rounded-lg border text-center transition-all ${\n                      selectedDuration === duration.minutes\n                        ? 'border-saffron-300 bg-saffron-50 text-saffron-700'\n                        : 'border-gray-200 hover:border-saffron-200'\n                    } ${isActive ? 'opacity-50 cursor-not-allowed' : ''}`}\n                  >\n                    <div className=\"font-medium\">{duration.label}</div>\n                    <div className=\"text-xs text-gray-500\">\n                      Level {duration.difficulty}\n                    </div>\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Controls */}\n            <div className=\"spiritual-card p-6\">\n              <div className=\"flex items-center justify-center space-x-4\">\n                {!isActive ? (\n                  <button\n                    onClick={startMeditation}\n                    className=\"karma-button flex items-center space-x-2\"\n                  >\n                    <Play className=\"w-5 h-5\" />\n                    <span>Begin</span>\n                  </button>\n                ) : (\n                  <button\n                    onClick={pauseMeditation}\n                    className=\"karma-button flex items-center space-x-2\"\n                  >\n                    <Pause className=\"w-5 h-5\" />\n                    <span>Pause</span>\n                  </button>\n                )}\n                \n                <button\n                  onClick={resetMeditation}\n                  className=\"px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2\"\n                >\n                  <RotateCcw className=\"w-4 h-4\" />\n                  <span>Reset</span>\n                </button>\n\n                <button\n                  onClick={() => {\n                    const newSoundState = !soundEnabled;\n                    setSoundEnabled(newSoundState);\n                    \n                    // Handle audio based on new sound state\n                    if (audioRef.current) {\n                      if (newSoundState && isActive) {\n                        audioRef.current.play().catch(e => console.log('Audio play failed:', e));\n                      } else {\n                        audioRef.current.pause();\n                      }\n                    }\n                  }}\n                  className=\"p-2 border border-gray-300 rounded-lg hover:bg-gray-50\"\n                >\n                  {soundEnabled ? <Volume2 className=\"w-4 h-4\" /> : <VolumeX className=\"w-4 h-4\" />}\n                </button>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Meditation Visualization */}\n          <motion.div\n            className=\"lg:col-span-2\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n          >\n            <div className=\"spiritual-card p-8 h-full flex flex-col items-center justify-center min-h-[500px]\">\n              {/* Timer Display */}\n              <motion.div\n                className=\"text-6xl font-mono font-bold text-saffron-700 mb-8\"\n                animate={{ scale: isActive ? [1, 1.05, 1] : 1 }}\n                transition={{ duration: 1, repeat: isActive ? Infinity : 0 }}\n              >\n                {formatTime(timeLeft)}\n              </motion.div>\n\n              {/* Meditation Visualization */}\n              <div className=\"relative w-80 h-80 flex items-center justify-center\">\n                {/* Breathing Circle */}\n                {meditationType === 'breathing' && (\n                  <motion.div\n                    className=\"absolute w-64 h-64 rounded-full border-4 border-saffron-300 flex items-center justify-center\"\n                    animate={{\n                      scale: breathPhase === 'inhale' ? 1.3 : breathPhase === 'exhale' ? 0.7 : 1,\n                      borderColor: breathPhase === 'inhale' ? '#10b981' : breathPhase === 'exhale' ? '#3b82f6' : '#f59e0b'\n                    }}\n                    transition={{ duration: breathPhase === 'inhale' ? 4 : breathPhase === 'exhale' ? 8 : 1 }}\n                  >\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl mb-2\">🌬️</div>\n                      <div className=\"text-lg font-medium text-gray-700\">\n                        {getBreathingInstruction()}\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Mantra Visualization */}\n                {meditationType === 'mantra' && (\n                  <motion.div\n                    className=\"text-center\"\n                    animate={{ scale: [1, 1.1, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <motion.div\n                      className=\"text-8xl mb-4\"\n                      animate={{ rotate: 360 }}\n                      transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n                    >\n                      🕉️\n                    </motion.div>\n                    <div className=\"sanskrit-text text-2xl text-saffron-700\">\n                      ॐ मणि पद्मे हूँ\n                    </div>\n                    <div className=\"text-sm text-gray-600 mt-2\">\n                      Om Mani Padme Hum\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Chakra Visualization */}\n                {meditationType === 'visualization' && (\n                  <div className=\"relative\">\n                    {[...Array(7)].map((_, i) => (\n                      <motion.div\n                        key={i}\n                        className=\"absolute w-12 h-12 rounded-full\"\n                        style={{\n                          backgroundColor: getChakraColor(i),\n                          top: `${i * 40}px`,\n                          left: '50%',\n                          transform: 'translateX(-50%)'\n                        }}\n                        animate={{\n                          scale: [1, 1.2, 1],\n                          opacity: [0.7, 1, 0.7]\n                        }}\n                        transition={{\n                          duration: 2,\n                          repeat: Infinity,\n                          delay: i * 0.3\n                        }}\n                      />\n                    ))}\n                    <div className=\"text-center mt-80\">\n                      <div className=\"text-lg font-medium text-gray-700\">\n                        Visualize energy flowing through chakras\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Mindfulness Visualization */}\n                {meditationType === 'mindfulness' && (\n                  <motion.div\n                    className=\"text-center\"\n                    animate={{ opacity: [0.5, 1, 0.5] }}\n                    transition={{ duration: 4, repeat: Infinity }}\n                  >\n                    <div className=\"text-6xl mb-4\">🧘‍♂️</div>\n                    <div className=\"text-lg font-medium text-gray-700 mb-2\">\n                      Be present in this moment\n                    </div>\n                    <div className=\"text-sm text-gray-600\">\n                      Observe thoughts without judgment\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Progress Ring */}\n                <svg className=\"absolute inset-0 w-full h-full -rotate-90\">\n                  <circle\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    r=\"150\"\n                    fill=\"none\"\n                    stroke=\"#f3f4f6\"\n                    strokeWidth=\"4\"\n                  />\n                  <motion.circle\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    r=\"150\"\n                    fill=\"none\"\n                    stroke=\"#f59e0b\"\n                    strokeWidth=\"4\"\n                    strokeLinecap=\"round\"\n                    strokeDasharray={`${2 * Math.PI * 150}`}\n                    strokeDashoffset={`${2 * Math.PI * 150 * (timeLeft / (selectedDuration * 60))}`}\n                    transition={{ duration: 1 }}\n                  />\n                </svg>\n              </div>\n\n              {/* Meditation Benefits */}\n              {!isActive && (\n                <motion.div\n                  className=\"mt-8 text-center\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: 0.5 }}\n                >\n                  <div className=\"text-sm text-gray-600 mb-2\">Benefits of this practice:</div>\n                  <div className=\"flex justify-center space-x-4\">\n                    {Object.entries(meditationTypes.find(t => t.type === meditationType)?.benefits || {}).map(([benefit, value]) => (\n                      <div key={benefit} className=\"text-center\">\n                        <div className=\"text-lg font-bold text-saffron-600\">+{value}</div>\n                        <div className=\"text-xs text-gray-500 capitalize\">{benefit}</div>\n                      </div>\n                    ))}\n                  </div>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MeditationCenter;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,YAAY,QAAQ,uBAAuB;AAEpD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACvC,MAAM;IAAEC,MAAM;IAAEC,kBAAkB;IAAEC,WAAW;IAAEC;EAAgB,CAAC,GAAGf,YAAY,CAAC,CAAC;EACnF,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAA2D,WAAW,CAAC;EAC3H,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAyC,QAAQ,CAAC;EAChG,MAAMgC,QAAQ,GAAG9B,MAAM,CAA0B,IAAI,CAAC;EAEtD,MAAM+B,eAAe,GAAGlC,KAAK,CAACmC,OAAO,CAAC,MAAO,CAC3C;IACEC,IAAI,EAAE,WAAoB;IAC1BC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,2CAA2C;IACxDC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,QAAiB;IACvBC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,+CAA+C;IAC5DC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAG;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,eAAwB;IAC9BC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,sCAAsC;IACnDC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAE;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,aAAsB;IAC5BC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,qCAAqC;IAClDC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG;EAC9C,CAAC,CACD,EAAE,EAAE,CAAC;EAEP,MAAMC,SAAS,GAAG5C,KAAK,CAACmC,OAAO,CAAC,MAAO,CACrC;IAAEU,OAAO,EAAE,CAAC;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAE,CAAC,EAC7C;IAAEF,OAAO,EAAE,CAAC;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAE,CAAC,EAC7C;IAAEF,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAE,CAAC,EAC/C;IAAEF,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAE,CAAC,EAC/C;IAAEF,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAE,CAAC,EAC/C;IAAEF,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAE,CAAC,CAC/C,EAAE,EAAE,CAAC;;EAEP;EACA,MAAMC,cAAc,GAAGhD,KAAK,CAACmC,OAAO,CAAC,OAAO;IAC1Cc,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE;EACT,CAAC,CAAC,EAAE,EAAE,CAAC;;EAEP;EACAlD,SAAS,CAAC,MAAM;IACd;IACAI,YAAY,CAAC+C,UAAU,CAACxB,YAAY,CAAC;IAErC,OAAO,MAAM;MACX;MACAvB,YAAY,CAACgD,OAAO,CAAC,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,CAACzB,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM0B,wBAAwB,GAAGvD,KAAK,CAACwD,WAAW,CAAC,MAAM;IACvD,IAAI,CAACvC,MAAM,EAAE;IAEb,MAAMwC,YAAY,GAAGvB,eAAe,CAACwB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvB,IAAI,KAAKT,cAAc,CAAE;IAC1E,MAAMiC,QAAQ,GAAGhB,SAAS,CAACc,IAAI,CAACG,CAAC,IAAIA,CAAC,CAAChB,OAAO,KAAKpB,gBAAgB,CAAE;IAErE,MAAMqC,OAA0B,GAAG;MACjCC,EAAE,EAAE,cAAcC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC9B7B,IAAI,EAAET,cAAc;MACpBiC,QAAQ,EAAEnC,gBAAgB;MAC1BsB,UAAU,EAAEa,QAAQ,CAACb,UAAU;MAC/BmB,OAAO,EAAET,YAAY,CAACjB;IACxB,CAAC;IAEDtB,kBAAkB,CAAC4C,OAAO,CAAC;IAC3B3C,WAAW,CAAC;MAAEsB,MAAM,EAAEgB,YAAY,CAACjB,QAAQ,CAACC;IAAO,CAAC,CAAC;IACrDrB,eAAe,CAAC,0BAA0BqC,YAAY,CAACjB,QAAQ,CAACC,MAAM,gBAAgB,CAAC;IAEvFnB,WAAW,CAAC,KAAK,CAAC;IAClBE,WAAW,CAACC,gBAAgB,GAAG,EAAE,CAAC;;IAElC;IACAnB,YAAY,CAACgD,OAAO,CAAC,CAAC;EACxB,CAAC,EAAE,CAACrC,MAAM,EAAEiB,eAAe,EAAEP,cAAc,EAAEiB,SAAS,EAAEnB,gBAAgB,EAAEP,kBAAkB,EAAEC,WAAW,EAAEC,eAAe,CAAC,CAAC;EAE5HlB,SAAS,CAAC,MAAM;IACd,IAAIiE,QAAwB;IAE5B,IAAI9C,QAAQ,IAAIE,QAAQ,GAAG,CAAC,EAAE;MAC5B4C,QAAQ,GAAGC,WAAW,CAAC,MAAM;QAC3B5C,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM,IAAIA,QAAQ,KAAK,CAAC,EAAE;MACzBgC,wBAAwB,CAAC,CAAC;IAC5B;IAEA,OAAO,MAAMc,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAC9C,QAAQ,EAAEE,QAAQ,EAAEgC,wBAAwB,CAAC,CAAC;;EAElD;EACArD,SAAS,CAAC,MAAM;IACd,IAAImB,QAAQ,IAAIM,cAAc,KAAK,WAAW,EAAE;MAC9C,MAAM2C,cAAc,GAAGA,CAAA,KAAM;QAC3BtC,cAAc,CAAC,QAAQ,CAAC;QACxBuC,UAAU,CAAC,MAAMvC,cAAc,CAAC,MAAM,CAAC,EAAEgB,cAAc,CAACC,MAAM,CAAC;QAC/DsB,UAAU,CAAC,MAAMvC,cAAc,CAAC,QAAQ,CAAC,EAAEgB,cAAc,CAACC,MAAM,GAAGD,cAAc,CAACE,IAAI,CAAC;QACvFqB,UAAU,CAAC,MAAMvC,cAAc,CAAC,OAAO,CAAC,EAAEgB,cAAc,CAACC,MAAM,GAAGD,cAAc,CAACE,IAAI,GAAGF,cAAc,CAACG,MAAM,CAAC;MAChH,CAAC;MAEDmB,cAAc,CAAC,CAAC;MAChB,MAAME,cAAc,GAAGJ,WAAW,CAACE,cAAc,EAAEG,MAAM,CAACC,MAAM,CAAC1B,cAAc,CAAC,CAAC2B,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,CAAC;MAE5G,OAAO,MAAMR,aAAa,CAACG,cAAc,CAAC;IAC5C;EACF,CAAC,EAAE,CAACnD,QAAQ,EAAEM,cAAc,EAAEqB,cAAc,CAAC,CAAC;EAE9C,MAAM8B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCtD,WAAW,CAACC,gBAAgB,GAAG,EAAE,CAAC;IAClCH,WAAW,CAAC,IAAI,CAAC;;IAEjB;IACA,IAAIO,YAAY,EAAE;MAChB,IAAI;QACF,MAAMvB,YAAY,CAACyE,mBAAmB,CAAC,CAAC;QACxCC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC3D;IACF;EACF,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B7D,WAAW,CAAC,CAACD,QAAQ,CAAC;;IAEtB;IACA,IAAIA,QAAQ,EAAE;MACZf,YAAY,CAAC8E,YAAY,CAAC,CAAC;IAC7B,CAAC,MAAM,IAAIvD,YAAY,EAAE;MACvBvB,YAAY,CAAC+E,aAAa,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BhE,WAAW,CAAC,KAAK,CAAC;IAClBE,WAAW,CAACC,gBAAgB,GAAG,EAAE,CAAC;IAClCO,cAAc,CAAC,QAAQ,CAAC;;IAExB;IACA1B,YAAY,CAACgD,OAAO,CAAC,CAAC;EACxB,CAAC;;EAED;EACA;EACA;;EAEA,MAAMiC,UAAU,GAAIC,OAAe,IAAK;IACtC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;EAED,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,QAAQhE,WAAW;MACjB,KAAK,QAAQ;QAAE,OAAO,eAAe;MACrC,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,gBAAgB;MACtC,KAAK,OAAO;QAAE,OAAO,UAAU;MAC/B;QAAS,OAAO,YAAY;IAC9B;EACF,CAAC;EAED,MAAMiE,cAAc,GAAIC,KAAa,IAAK;IACxC,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;IAC5F,OAAOA,MAAM,CAACD,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC;EACtC,CAAC;EAED,oBACEtF,OAAA;IAAKuF,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrCxF,OAAA;MAAKuF,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhCxF,OAAA,CAACT,MAAM,CAACkG,GAAG;QACTF,SAAS,EAAC,kBAAkB;QAC5BG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAE9BxF,OAAA;UAAIuF,SAAS,EAAC,yDAAyD;UAAAC,QAAA,EAAC;QAExE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLjG,OAAA;UAAGuF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEbjG,OAAA;QAAKuF,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDxF,OAAA,CAACT,MAAM,CAACkG,GAAG;UACTF,SAAS,EAAC,yBAAyB;UACnCG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEO,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCL,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEO,CAAC,EAAE;UAAE,CAAE;UAAAV,QAAA,gBAG9BxF,OAAA;YAAKuF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCxF,OAAA;cAAIuF,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE5D;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjG,OAAA;cAAKuF,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBnE,eAAe,CAAC8E,GAAG,CAAE5E,IAAI,iBACxBvB,OAAA;gBAEEoG,OAAO,EAAEA,CAAA,KAAMrF,iBAAiB,CAACQ,IAAI,CAACA,IAAI,CAAE;gBAC5C8E,QAAQ,EAAE7F,QAAS;gBACnB+E,SAAS,EAAE,yDACTzE,cAAc,KAAKS,IAAI,CAACA,IAAI,GACxB,kCAAkC,GAClC,0CAA0C,IAC5Cf,QAAQ,GAAG,+BAA+B,GAAG,EAAE,EAAG;gBAAAgF,QAAA,eAEtDxF,OAAA;kBAAKuF,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CxF,OAAA;oBAAMuF,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAEjE,IAAI,CAACG;kBAAI;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7CjG,OAAA;oBAAAwF,QAAA,gBACExF,OAAA;sBAAKuF,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAEjE,IAAI,CAACC;oBAAK;sBAAAsE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7DjG,OAAA;sBAAKuF,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEjE,IAAI,CAACE;oBAAW;sBAAAqE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAfD1E,IAAI,CAACA,IAAI;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBR,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjG,OAAA;YAAKuF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCxF,OAAA;cAAIuF,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE5D;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLjG,OAAA;cAAKuF,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpCzD,SAAS,CAACoE,GAAG,CAAEpD,QAAQ,iBACtB/C,OAAA;gBAEEoG,OAAO,EAAEA,CAAA,KAAMvF,mBAAmB,CAACkC,QAAQ,CAACf,OAAO,CAAE;gBACrDqE,QAAQ,EAAE7F,QAAS;gBACnB+E,SAAS,EAAE,oDACT3E,gBAAgB,KAAKmC,QAAQ,CAACf,OAAO,GACjC,mDAAmD,GACnD,0CAA0C,IAC5CxB,QAAQ,GAAG,+BAA+B,GAAG,EAAE,EAAG;gBAAAgF,QAAA,gBAEtDxF,OAAA;kBAAKuF,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEzC,QAAQ,CAACd;gBAAK;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDjG,OAAA;kBAAKuF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,QAC/B,EAACzC,QAAQ,CAACb,UAAU;gBAAA;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA,GAZDlD,QAAQ,CAACf,OAAO;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaf,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjG,OAAA;YAAKuF,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjCxF,OAAA;cAAKuF,SAAS,EAAC,4CAA4C;cAAAC,QAAA,GACxD,CAAChF,QAAQ,gBACRR,OAAA;gBACEoG,OAAO,EAAEnC,eAAgB;gBACzBsB,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBAEpDxF,OAAA,CAACN,IAAI;kBAAC6F,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5BjG,OAAA;kBAAAwF,QAAA,EAAM;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,gBAETjG,OAAA;gBACEoG,OAAO,EAAE9B,eAAgB;gBACzBiB,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBAEpDxF,OAAA,CAACL,KAAK;kBAAC4F,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7BjG,OAAA;kBAAAwF,QAAA,EAAM;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CACT,eAEDjG,OAAA;gBACEoG,OAAO,EAAE3B,eAAgB;gBACzBc,SAAS,EAAC,0FAA0F;gBAAAC,QAAA,gBAEpGxF,OAAA,CAACJ,SAAS;kBAAC2F,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjCjG,OAAA;kBAAAwF,QAAA,EAAM;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAETjG,OAAA;gBACEoG,OAAO,EAAEA,CAAA,KAAM;kBACb,MAAME,aAAa,GAAG,CAACtF,YAAY;kBACnCC,eAAe,CAACqF,aAAa,CAAC;;kBAE9B;kBACA,IAAIlF,QAAQ,CAACmF,OAAO,EAAE;oBACpB,IAAID,aAAa,IAAI9F,QAAQ,EAAE;sBAC7BY,QAAQ,CAACmF,OAAO,CAACC,IAAI,CAAC,CAAC,CAACC,KAAK,CAACC,CAAC,IAAIvC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEsC,CAAC,CAAC,CAAC;oBAC1E,CAAC,MAAM;sBACLtF,QAAQ,CAACmF,OAAO,CAAChE,KAAK,CAAC,CAAC;oBAC1B;kBACF;gBACF,CAAE;gBACFgD,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,EAEjExE,YAAY,gBAAGhB,OAAA,CAACH,OAAO;kBAAC0F,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGjG,OAAA,CAACF,OAAO;kBAACyF,SAAS,EAAC;gBAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbjG,OAAA,CAACT,MAAM,CAACkG,GAAG;UACTF,SAAS,EAAC,eAAe;UACzBG,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEgB,KAAK,EAAE;UAAI,CAAE;UACpCd,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEgB,KAAK,EAAE;UAAE,CAAE;UAAAnB,QAAA,eAElCxF,OAAA;YAAKuF,SAAS,EAAC,mFAAmF;YAAAC,QAAA,gBAEhGxF,OAAA,CAACT,MAAM,CAACkG,GAAG;cACTF,SAAS,EAAC,oDAAoD;cAC9DM,OAAO,EAAE;gBAAEc,KAAK,EAAEnG,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG;cAAE,CAAE;cAChDoG,UAAU,EAAE;gBAAE7D,QAAQ,EAAE,CAAC;gBAAE8D,MAAM,EAAErG,QAAQ,GAAGsG,QAAQ,GAAG;cAAE,CAAE;cAAAtB,QAAA,EAE5Dd,UAAU,CAAChE,QAAQ;YAAC;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eAGbjG,OAAA;cAAKuF,SAAS,EAAC,qDAAqD;cAAAC,QAAA,GAEjE1E,cAAc,KAAK,WAAW,iBAC7Bd,OAAA,CAACT,MAAM,CAACkG,GAAG;gBACTF,SAAS,EAAC,8FAA8F;gBACxGM,OAAO,EAAE;kBACPc,KAAK,EAAEzF,WAAW,KAAK,QAAQ,GAAG,GAAG,GAAGA,WAAW,KAAK,QAAQ,GAAG,GAAG,GAAG,CAAC;kBAC1E6F,WAAW,EAAE7F,WAAW,KAAK,QAAQ,GAAG,SAAS,GAAGA,WAAW,KAAK,QAAQ,GAAG,SAAS,GAAG;gBAC7F,CAAE;gBACF0F,UAAU,EAAE;kBAAE7D,QAAQ,EAAE7B,WAAW,KAAK,QAAQ,GAAG,CAAC,GAAGA,WAAW,KAAK,QAAQ,GAAG,CAAC,GAAG;gBAAE,CAAE;gBAAAsE,QAAA,eAE1FxF,OAAA;kBAAKuF,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BxF,OAAA;oBAAKuF,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAG;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxCjG,OAAA;oBAAKuF,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC/CN,uBAAuB,CAAC;kBAAC;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb,EAGAnF,cAAc,KAAK,QAAQ,iBAC1Bd,OAAA,CAACT,MAAM,CAACkG,GAAG;gBACTF,SAAS,EAAC,aAAa;gBACvBM,OAAO,EAAE;kBAAEc,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;gBAAE,CAAE;gBAChCC,UAAU,EAAE;kBAAE7D,QAAQ,EAAE,CAAC;kBAAE8D,MAAM,EAAEC;gBAAS,CAAE;gBAAAtB,QAAA,gBAE9CxF,OAAA,CAACT,MAAM,CAACkG,GAAG;kBACTF,SAAS,EAAC,eAAe;kBACzBM,OAAO,EAAE;oBAAEmB,MAAM,EAAE;kBAAI,CAAE;kBACzBJ,UAAU,EAAE;oBAAE7D,QAAQ,EAAE,EAAE;oBAAE8D,MAAM,EAAEC,QAAQ;oBAAEG,IAAI,EAAE;kBAAS,CAAE;kBAAAzB,QAAA,EAChE;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbjG,OAAA;kBAAKuF,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAEzD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNjG,OAAA;kBAAKuF,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE5C;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb,EAGAnF,cAAc,KAAK,eAAe,iBACjCd,OAAA;gBAAKuF,SAAS,EAAC,UAAU;gBAAAC,QAAA,GACtB,CAAC,GAAG0B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACf,GAAG,CAAC,CAACgB,CAAC,EAAEC,CAAC,kBACtBpH,OAAA,CAACT,MAAM,CAACkG,GAAG;kBAETF,SAAS,EAAC,iCAAiC;kBAC3C8B,KAAK,EAAE;oBACLC,eAAe,EAAEnC,cAAc,CAACiC,CAAC,CAAC;oBAClCG,GAAG,EAAE,GAAGH,CAAC,GAAG,EAAE,IAAI;oBAClBI,IAAI,EAAE,KAAK;oBACXC,SAAS,EAAE;kBACb,CAAE;kBACF5B,OAAO,EAAE;oBACPc,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;oBAClBhB,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;kBACvB,CAAE;kBACFiB,UAAU,EAAE;oBACV7D,QAAQ,EAAE,CAAC;oBACX8D,MAAM,EAAEC,QAAQ;oBAChBY,KAAK,EAAEN,CAAC,GAAG;kBACb;gBAAE,GAhBGA,CAAC;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiBP,CACF,CAAC,eACFjG,OAAA;kBAAKuF,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,eAChCxF,OAAA;oBAAKuF,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAEnD;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGAnF,cAAc,KAAK,aAAa,iBAC/Bd,OAAA,CAACT,MAAM,CAACkG,GAAG;gBACTF,SAAS,EAAC,aAAa;gBACvBM,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;gBAAE,CAAE;gBACpCiB,UAAU,EAAE;kBAAE7D,QAAQ,EAAE,CAAC;kBAAE8D,MAAM,EAAEC;gBAAS,CAAE;gBAAAtB,QAAA,gBAE9CxF,OAAA;kBAAKuF,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1CjG,OAAA;kBAAKuF,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAExD;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNjG,OAAA;kBAAKuF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAEvC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb,eAGDjG,OAAA;gBAAKuF,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,gBACxDxF,OAAA;kBACE2H,EAAE,EAAC,KAAK;kBACRC,EAAE,EAAC,KAAK;kBACRC,CAAC,EAAC,KAAK;kBACPC,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC;gBAAG;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACFjG,OAAA,CAACT,MAAM,CAAC0I,MAAM;kBACZN,EAAE,EAAC,KAAK;kBACRC,EAAE,EAAC,KAAK;kBACRC,CAAC,EAAC,KAAK;kBACPC,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfE,aAAa,EAAC,OAAO;kBACrBC,eAAe,EAAE,GAAG,CAAC,GAAGtD,IAAI,CAACuD,EAAE,GAAG,GAAG,EAAG;kBACxCC,gBAAgB,EAAE,GAAG,CAAC,GAAGxD,IAAI,CAACuD,EAAE,GAAG,GAAG,IAAI1H,QAAQ,IAAIE,gBAAgB,GAAG,EAAE,CAAC,CAAC,EAAG;kBAChFgG,UAAU,EAAE;oBAAE7D,QAAQ,EAAE;kBAAE;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL,CAACzF,QAAQ,iBACRR,OAAA,CAACT,MAAM,CAACkG,GAAG;cACTF,SAAS,EAAC,kBAAkB;cAC5BG,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACxBE,OAAO,EAAE;gBAAEF,OAAO,EAAE;cAAE,CAAE;cACxBiB,UAAU,EAAE;gBAAEc,KAAK,EAAE;cAAI,CAAE;cAAAlC,QAAA,gBAE3BxF,OAAA;gBAAKuF,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAA0B;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5EjG,OAAA;gBAAKuF,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAC3C5B,MAAM,CAAC0E,OAAO,CAAC,EAAAnI,qBAAA,GAAAkB,eAAe,CAACwB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvB,IAAI,KAAKT,cAAc,CAAC,cAAAX,qBAAA,uBAApDA,qBAAA,CAAsDwB,QAAQ,KAAI,CAAC,CAAC,CAAC,CAACwE,GAAG,CAAC,CAAC,CAACoC,OAAO,EAAEC,KAAK,CAAC,kBACzGxI,OAAA;kBAAmBuF,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBACxCxF,OAAA;oBAAKuF,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,GAAC,EAACgD,KAAK;kBAAA;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClEjG,OAAA;oBAAKuF,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAE+C;kBAAO;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GAFzDsC,OAAO;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGZ,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/F,EAAA,CA3dID,gBAA0B;EAAA,QACuCT,YAAY;AAAA;AAAAiJ,EAAA,GAD7ExI,gBAA0B;AA6dhC,eAAeA,gBAAgB;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}