{"name": "karmaverse", "version": "1.0.0", "description": "An Interactive Hindu Philosophy & Spiritual Journey Simulator", "private": true, "dependencies": {"@react-three/drei": "^9.88.13", "@react-three/fiber": "^8.15.11", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/three": "^0.158.3", "chart.js": "^4.4.0", "framer-motion": "^6.5.1", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "three": "^0.158.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zustand": "^4.4.7"}, "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "deploy": "npm run build && npx vercel --prod"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": ["hinduism", "spirituality", "karma", "dharma", "meditation", "philosophy", "bhagavad-gita", "upanishads", "yoga", "moksha", "react", "typescript", "game", "simulation"], "author": "KarmaVerse Development Team", "license": "MIT", "homepage": "https://karmaverse.vercel.app", "repository": {"type": "git", "url": "https://github.com/your-username/karmaverse.git"}}