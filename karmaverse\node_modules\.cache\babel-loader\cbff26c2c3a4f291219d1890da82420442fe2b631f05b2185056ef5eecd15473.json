{"ast": null, "code": "var _jsxFileName = \"D:\\\\Projects\\\\Master1\\\\karmaverse\\\\src\\\\components\\\\Meditation\\\\MeditationCenter.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { useGameStore } from '../../store/gameStore';\nimport { Play, Pause, RotateCcw, Volume2, VolumeX } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MeditationCenter = () => {\n  _s();\n  var _meditationTypes$find;\n  const {\n    avatar,\n    completeMeditation,\n    updateGunas,\n    addNotification\n  } = useGameStore();\n  const [isActive, setIsActive] = useState(false);\n  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes default\n  const [selectedDuration, setSelectedDuration] = useState(5);\n  const [meditationType, setMeditationType] = useState('breathing');\n  const [soundEnabled, setSoundEnabled] = useState(true);\n  const [breathPhase, setBreathPhase] = useState('inhale');\n  const audioRef = useRef(null);\n  const meditationTypes = React.useMemo(() => [{\n    type: 'breathing',\n    title: 'Pranayama',\n    description: 'Breath control meditation for inner peace',\n    icon: '🌬️',\n    benefits: {\n      sattva: 8,\n      focus: 10,\n      peace: 12\n    }\n  }, {\n    type: 'mantra',\n    title: 'Mantra Japa',\n    description: 'Sacred sound repetition for divine connection',\n    icon: '🕉️',\n    benefits: {\n      sattva: 12,\n      focus: 8,\n      peace: 10\n    }\n  }, {\n    type: 'visualization',\n    title: 'Dhyana',\n    description: 'Visualization meditation for clarity',\n    icon: '👁️',\n    benefits: {\n      sattva: 10,\n      focus: 12,\n      peace: 8\n    }\n  }, {\n    type: 'mindfulness',\n    title: 'Vipassana',\n    description: 'Mindful awareness of present moment',\n    icon: '🧘‍♂️',\n    benefits: {\n      sattva: 9,\n      focus: 11,\n      peace: 10\n    }\n  }], []);\n  const durations = React.useMemo(() => [{\n    minutes: 3,\n    label: '3 min',\n    difficulty: 1\n  }, {\n    minutes: 5,\n    label: '5 min',\n    difficulty: 2\n  }, {\n    minutes: 10,\n    label: '10 min',\n    difficulty: 3\n  }, {\n    minutes: 15,\n    label: '15 min',\n    difficulty: 4\n  }, {\n    minutes: 20,\n    label: '20 min',\n    difficulty: 5\n  }, {\n    minutes: 30,\n    label: '30 min',\n    difficulty: 6\n  }], []);\n\n  // Breathing cycle timing (4-7-8 technique)\n  const breathingCycle = React.useMemo(() => ({\n    inhale: 4000,\n    hold: 7000,\n    exhale: 8000,\n    pause: 1000\n  }), []);\n\n  // Initialize audio on component mount\n  useEffect(() => {\n    // Use the original filename with spaces and commas\n    const audioPath = process.env.NODE_ENV === 'production' ? '/2%20minutes%20of%20relaxing%20music,2%20minute%20meditation%20music,2%20minutes%20meditation,music%202%20minute.mp3' : process.env.PUBLIC_URL + '/2%20minutes%20of%20relaxing%20music,2%20minute%20meditation%20music,2%20minutes%20meditation,music%202%20minute.mp3';\n    audioRef.current = new Audio(audioPath);\n    audioRef.current.loop = true;\n    audioRef.current.volume = 0.5;\n    return () => {\n      if (audioRef.current) {\n        audioRef.current.pause();\n        audioRef.current = null;\n      }\n    };\n  }, []);\n\n  // Move handleMeditationComplete above this useEffect to avoid TS2448 error\n  const handleMeditationComplete = React.useCallback(() => {\n    if (!avatar) return;\n    const selectedType = meditationTypes.find(t => t.type === meditationType);\n    const duration = durations.find(d => d.minutes === selectedDuration);\n    const session = {\n      id: `meditation_${Date.now()}`,\n      type: meditationType,\n      duration: selectedDuration,\n      difficulty: duration.difficulty,\n      rewards: selectedType.benefits\n    };\n    completeMeditation(session);\n    updateGunas({\n      sattva: selectedType.benefits.sattva\n    });\n    addNotification(`Meditation completed! +${selectedType.benefits.sattva} Sattva gained`);\n    setIsActive(false);\n    setTimeLeft(selectedDuration * 60);\n\n    // Stop audio when meditation is complete\n    if (audioRef.current) {\n      audioRef.current.pause();\n      audioRef.current.currentTime = 0;\n    }\n  }, [avatar, meditationTypes, meditationType, durations, selectedDuration, completeMeditation, updateGunas, addNotification]);\n  useEffect(() => {\n    let interval;\n    if (isActive && timeLeft > 0) {\n      interval = setInterval(() => {\n        setTimeLeft(timeLeft - 1);\n      }, 1000);\n    } else if (timeLeft === 0) {\n      handleMeditationComplete();\n    }\n    return () => clearInterval(interval);\n  }, [isActive, timeLeft, handleMeditationComplete]);\n\n  // Breathing animation cycle\n  useEffect(() => {\n    if (isActive && meditationType === 'breathing') {\n      const cycleBreathing = () => {\n        setBreathPhase('inhale');\n        setTimeout(() => setBreathPhase('hold'), breathingCycle.inhale);\n        setTimeout(() => setBreathPhase('exhale'), breathingCycle.inhale + breathingCycle.hold);\n        setTimeout(() => setBreathPhase('pause'), breathingCycle.inhale + breathingCycle.hold + breathingCycle.exhale);\n      };\n      cycleBreathing();\n      const breathInterval = setInterval(cycleBreathing, Object.values(breathingCycle).reduce((a, b) => a + b, 0));\n      return () => clearInterval(breathInterval);\n    }\n  }, [isActive, meditationType, breathingCycle]);\n  const startMeditation = () => {\n    setTimeLeft(selectedDuration * 60);\n    setIsActive(true);\n\n    // Play audio when meditation starts if sound is enabled\n    if (soundEnabled && audioRef.current) {\n      audioRef.current.play().then(() => console.log('Audio playing successfully')).catch(e => {\n        console.error('Audio play failed:', e);\n        // Try with the fallback audio element\n        const fallbackAudio = document.querySelector('audio');\n        if (fallbackAudio) {\n          fallbackAudio.play().then(() => console.log('Fallback audio playing')).catch(err => console.error('Fallback audio failed too:', err));\n        }\n      });\n    }\n  };\n  const pauseMeditation = () => {\n    setIsActive(!isActive);\n\n    // Pause or play audio based on meditation state\n    if (isActive && audioRef.current) {\n      audioRef.current.pause();\n    } else if (soundEnabled && audioRef.current) {\n      audioRef.current.play().catch(e => console.log('Audio play failed:', e));\n    }\n  };\n  const resetMeditation = () => {\n    setIsActive(false);\n    setTimeLeft(selectedDuration * 60);\n    setBreathPhase('inhale');\n\n    // Stop audio when meditation is reset\n    if (audioRef.current) {\n      audioRef.current.pause();\n      audioRef.current.currentTime = 0;\n    }\n  };\n\n  // ...existing code...\n  // (Removed duplicate handleMeditationComplete declaration)\n  // ...existing code...\n\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n  const getBreathingInstruction = () => {\n    switch (breathPhase) {\n      case 'inhale':\n        return 'Breathe In...';\n      case 'hold':\n        return 'Hold...';\n      case 'exhale':\n        return 'Breathe Out...';\n      case 'pause':\n        return 'Pause...';\n      default:\n        return 'Breathe...';\n    }\n  };\n  const getChakraColor = index => {\n    const colors = ['#ff0000', '#ff8c00', '#ffff00', '#00ff00', '#0000ff', '#4b0082', '#9400d3'];\n    return colors[index % colors.length];\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen pt-20 p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"audio\", {\n      src: process.env.NODE_ENV === 'production' ? '/2%20minutes%20of%20relaxing%20music,2%20minute%20meditation%20music,2%20minutes%20meditation,music%202%20minute.mp3' : process.env.PUBLIC_URL + '/2%20minutes%20of%20relaxing%20music,2%20minute%20meditation%20music,2%20minutes%20meditation,music%202%20minute.mp3',\n      loop: true,\n      preload: \"auto\",\n      style: {\n        display: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"text-center mb-8\",\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl font-spiritual font-bold text-saffron-700 mb-2\",\n          children: \"Meditation Center\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Find inner peace through ancient practices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"lg:col-span-1 space-y-6\",\n          initial: {\n            opacity: 0,\n            x: -20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spiritual-card p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-4 text-saffron-700\",\n              children: \"Choose Practice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: meditationTypes.map(type => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setMeditationType(type.type),\n                disabled: isActive,\n                className: `w-full text-left p-3 rounded-lg border transition-all ${meditationType === type.type ? 'border-saffron-300 bg-saffron-50' : 'border-gray-200 hover:border-saffron-200'} ${isActive ? 'opacity-50 cursor-not-allowed' : ''}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-2xl\",\n                    children: type.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-gray-800\",\n                      children: type.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-600\",\n                      children: type.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this)\n              }, type.type, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spiritual-card p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-4 text-saffron-700\",\n              children: \"Duration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-2\",\n              children: durations.map(duration => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSelectedDuration(duration.minutes),\n                disabled: isActive,\n                className: `p-3 rounded-lg border text-center transition-all ${selectedDuration === duration.minutes ? 'border-saffron-300 bg-saffron-50 text-saffron-700' : 'border-gray-200 hover:border-saffron-200'} ${isActive ? 'opacity-50 cursor-not-allowed' : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium\",\n                  children: duration.label\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: [\"Level \", duration.difficulty]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this)]\n              }, duration.minutes, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spiritual-card p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center space-x-4\",\n              children: [!isActive ? /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: startMeditation,\n                className: \"karma-button flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Play, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Begin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: pauseMeditation,\n                className: \"karma-button flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Pause, {\n                  className: \"w-5 h-5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Pause\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: resetMeditation,\n                className: \"px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(RotateCcw, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Reset\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  const newSoundState = !soundEnabled;\n                  setSoundEnabled(newSoundState);\n\n                  // Handle audio based on new sound state\n                  if (audioRef.current) {\n                    if (newSoundState && isActive) {\n                      audioRef.current.play().catch(e => console.log('Audio play failed:', e));\n                    } else {\n                      audioRef.current.pause();\n                    }\n                  }\n                },\n                className: \"p-2 border border-gray-300 rounded-lg hover:bg-gray-50\",\n                children: soundEnabled ? /*#__PURE__*/_jsxDEV(Volume2, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 35\n                }, this) : /*#__PURE__*/_jsxDEV(VolumeX, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 69\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"lg:col-span-2\",\n          initial: {\n            opacity: 0,\n            scale: 0.9\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spiritual-card p-8 h-full flex flex-col items-center justify-center min-h-[500px]\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"text-6xl font-mono font-bold text-saffron-700 mb-8\",\n              animate: {\n                scale: isActive ? [1, 1.05, 1] : 1\n              },\n              transition: {\n                duration: 1,\n                repeat: isActive ? Infinity : 0\n              },\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative w-80 h-80 flex items-center justify-center\",\n              children: [meditationType === 'breathing' && /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute w-64 h-64 rounded-full border-4 border-saffron-300 flex items-center justify-center\",\n                animate: {\n                  scale: breathPhase === 'inhale' ? 1.3 : breathPhase === 'exhale' ? 0.7 : 1,\n                  borderColor: breathPhase === 'inhale' ? '#10b981' : breathPhase === 'exhale' ? '#3b82f6' : '#f59e0b'\n                },\n                transition: {\n                  duration: breathPhase === 'inhale' ? 4 : breathPhase === 'exhale' ? 8 : 1\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-2xl mb-2\",\n                    children: \"\\uD83C\\uDF2C\\uFE0F\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-lg font-medium text-gray-700\",\n                    children: getBreathingInstruction()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this), meditationType === 'mantra' && /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"text-center\",\n                animate: {\n                  scale: [1, 1.1, 1]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity\n                },\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"text-8xl mb-4\",\n                  animate: {\n                    rotate: 360\n                  },\n                  transition: {\n                    duration: 20,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                  },\n                  children: \"\\uD83D\\uDD49\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"sanskrit-text text-2xl text-saffron-700\",\n                  children: \"\\u0950 \\u092E\\u0923\\u093F \\u092A\\u0926\\u094D\\u092E\\u0947 \\u0939\\u0942\\u0901\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600 mt-2\",\n                  children: \"Om Mani Padme Hum\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this), meditationType === 'visualization' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [[...Array(7)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute w-12 h-12 rounded-full\",\n                  style: {\n                    backgroundColor: getChakraColor(i),\n                    top: `${i * 40}px`,\n                    left: '50%',\n                    transform: 'translateX(-50%)'\n                  },\n                  animate: {\n                    scale: [1, 1.2, 1],\n                    opacity: [0.7, 1, 0.7]\n                  },\n                  transition: {\n                    duration: 2,\n                    repeat: Infinity,\n                    delay: i * 0.3\n                  }\n                }, i, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 23\n                }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mt-80\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-lg font-medium text-gray-700\",\n                    children: \"Visualize energy flowing through chakras\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 440,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 19\n              }, this), meditationType === 'mindfulness' && /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"text-center\",\n                animate: {\n                  opacity: [0.5, 1, 0.5]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-6xl mb-4\",\n                  children: \"\\uD83E\\uDDD8\\u200D\\u2642\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-lg font-medium text-gray-700 mb-2\",\n                  children: \"Be present in this moment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Observe thoughts without judgment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"absolute inset-0 w-full h-full -rotate-90\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  cx: \"50%\",\n                  cy: \"50%\",\n                  r: \"150\",\n                  fill: \"none\",\n                  stroke: \"#f3f4f6\",\n                  strokeWidth: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.circle, {\n                  cx: \"50%\",\n                  cy: \"50%\",\n                  r: \"150\",\n                  fill: \"none\",\n                  stroke: \"#f59e0b\",\n                  strokeWidth: \"4\",\n                  strokeLinecap: \"round\",\n                  strokeDasharray: `${2 * Math.PI * 150}`,\n                  strokeDashoffset: `${2 * Math.PI * 150 * (timeLeft / (selectedDuration * 60))}`,\n                  transition: {\n                    duration: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 15\n            }, this), !isActive && /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"mt-8 text-center\",\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              transition: {\n                delay: 0.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 mb-2\",\n                children: \"Benefits of this practice:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-center space-x-4\",\n                children: Object.entries(((_meditationTypes$find = meditationTypes.find(t => t.type === meditationType)) === null || _meditationTypes$find === void 0 ? void 0 : _meditationTypes$find.benefits) || {}).map(([benefit, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-lg font-bold text-saffron-600\",\n                    children: [\"+\", value]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500 capitalize\",\n                    children: benefit\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 25\n                  }, this)]\n                }, benefit, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 215,\n    columnNumber: 5\n  }, this);\n};\n_s(MeditationCenter, \"ZmCCFnSatb0aHaQzcyvsS5+GctI=\", false, function () {\n  return [useGameStore];\n});\n_c = MeditationCenter;\nexport default MeditationCenter;\nvar _c;\n$RefreshReg$(_c, \"MeditationCenter\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "motion", "useGameStore", "Play", "Pause", "RotateCcw", "Volume2", "VolumeX", "jsxDEV", "_jsxDEV", "MeditationCenter", "_s", "_meditationTypes$find", "avatar", "completeMeditation", "updateGunas", "addNotification", "isActive", "setIsActive", "timeLeft", "setTimeLeft", "selectedDuration", "setSelectedDuration", "meditationType", "setMeditationType", "soundEnabled", "setSoundEnabled", "breathPhase", "setBreathPhase", "audioRef", "meditationTypes", "useMemo", "type", "title", "description", "icon", "benefits", "sattva", "focus", "peace", "durations", "minutes", "label", "difficulty", "breathingCycle", "inhale", "hold", "exhale", "pause", "audioPath", "process", "env", "NODE_ENV", "PUBLIC_URL", "current", "Audio", "loop", "volume", "handleMeditationComplete", "useCallback", "selectedType", "find", "t", "duration", "d", "session", "id", "Date", "now", "rewards", "currentTime", "interval", "setInterval", "clearInterval", "cycleBreathing", "setTimeout", "breathInterval", "Object", "values", "reduce", "a", "b", "startMeditation", "play", "then", "console", "log", "catch", "e", "error", "fallback<PERSON><PERSON><PERSON>", "document", "querySelector", "err", "pauseMeditation", "resetMeditation", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "getBreathingInstruction", "getChakraColor", "index", "colors", "length", "className", "children", "src", "preload", "style", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "x", "map", "onClick", "disabled", "newSoundState", "scale", "transition", "repeat", "Infinity", "borderColor", "rotate", "ease", "Array", "_", "i", "backgroundColor", "top", "left", "transform", "delay", "cx", "cy", "r", "fill", "stroke", "strokeWidth", "circle", "strokeLinecap", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PI", "strokeDashoffset", "entries", "benefit", "value", "_c", "$RefreshReg$"], "sources": ["D:/Projects/Master1/karmaverse/src/components/Meditation/MeditationCenter.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { useGameStore } from '../../store/gameStore';\nimport { MeditationSession } from '../../types';\nimport { audioService } from '../../services/audioService';\nimport { Play, Pause, RotateCcw, Volume2, VolumeX } from 'lucide-react';\n\nconst MeditationCenter: React.FC = () => {\n  const { avatar, completeMeditation, updateGunas, addNotification } = useGameStore();\n  const [isActive, setIsActive] = useState(false);\n  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes default\n  const [selectedDuration, setSelectedDuration] = useState(5);\n  const [meditationType, setMeditationType] = useState<'breathing' | 'mantra' | 'visualization' | 'mindfulness'>('breathing');\n  const [soundEnabled, setSoundEnabled] = useState(true);\n  const [breathPhase, setBreathPhase] = useState<'inhale' | 'hold' | 'exhale' | 'pause'>('inhale');\n  const audioRef = useRef<HTMLAudioElement | null>(null);\n\n  const meditationTypes = React.useMemo(() => ([\n    {\n      type: 'breathing' as const,\n      title: 'Pranayama',\n      description: 'Breath control meditation for inner peace',\n      icon: '🌬️',\n      benefits: { sattva: 8, focus: 10, peace: 12 }\n    },\n    {\n      type: 'mantra' as const,\n      title: 'Mantra Japa',\n      description: 'Sacred sound repetition for divine connection',\n      icon: '🕉️',\n      benefits: { sattva: 12, focus: 8, peace: 10 }\n    },\n    {\n      type: 'visualization' as const,\n      title: 'Dhyana',\n      description: 'Visualization meditation for clarity',\n      icon: '👁️',\n      benefits: { sattva: 10, focus: 12, peace: 8 }\n    },\n    {\n      type: 'mindfulness' as const,\n      title: 'Vipassana',\n      description: 'Mindful awareness of present moment',\n      icon: '🧘‍♂️',\n      benefits: { sattva: 9, focus: 11, peace: 10 }\n    }\n  ]), []);\n\n  const durations = React.useMemo(() => ([\n    { minutes: 3, label: '3 min', difficulty: 1 },\n    { minutes: 5, label: '5 min', difficulty: 2 },\n    { minutes: 10, label: '10 min', difficulty: 3 },\n    { minutes: 15, label: '15 min', difficulty: 4 },\n    { minutes: 20, label: '20 min', difficulty: 5 },\n    { minutes: 30, label: '30 min', difficulty: 6 }\n  ]), []);\n\n  // Breathing cycle timing (4-7-8 technique)\n  const breathingCycle = React.useMemo(() => ({\n    inhale: 4000,\n    hold: 7000,\n    exhale: 8000,\n    pause: 1000\n  }), []);\n\n  // Initialize audio on component mount\n  useEffect(() => {\n    // Use the original filename with spaces and commas\n    const audioPath =\n      process.env.NODE_ENV === 'production'\n        ? '/2%20minutes%20of%20relaxing%20music,2%20minute%20meditation%20music,2%20minutes%20meditation,music%202%20minute.mp3'\n        : process.env.PUBLIC_URL + '/2%20minutes%20of%20relaxing%20music,2%20minute%20meditation%20music,2%20minutes%20meditation,music%202%20minute.mp3';\n    audioRef.current = new Audio(audioPath);\n    audioRef.current.loop = true;\n    audioRef.current.volume = 0.5;\n    \n    return () => {\n      if (audioRef.current) {\n        audioRef.current.pause();\n        audioRef.current = null;\n      }\n    };\n  }, []);\n\n  // Move handleMeditationComplete above this useEffect to avoid TS2448 error\n  const handleMeditationComplete = React.useCallback(() => {\n    if (!avatar) return;\n\n    const selectedType = meditationTypes.find(t => t.type === meditationType)!;\n    const duration = durations.find(d => d.minutes === selectedDuration)!;\n\n    const session: MeditationSession = {\n      id: `meditation_${Date.now()}`,\n      type: meditationType,\n      duration: selectedDuration,\n      difficulty: duration.difficulty,\n      rewards: selectedType.benefits\n    };\n\n    completeMeditation(session);\n    updateGunas({ sattva: selectedType.benefits.sattva });\n    addNotification(`Meditation completed! +${selectedType.benefits.sattva} Sattva gained`);\n    \n    setIsActive(false);\n    setTimeLeft(selectedDuration * 60);\n    \n    // Stop audio when meditation is complete\n    if (audioRef.current) {\n      audioRef.current.pause();\n      audioRef.current.currentTime = 0;\n    }\n  }, [avatar, meditationTypes, meditationType, durations, selectedDuration, completeMeditation, updateGunas, addNotification]);\n\n  useEffect(() => {\n    let interval: NodeJS.Timeout;\n    \n    if (isActive && timeLeft > 0) {\n      interval = setInterval(() => {\n        setTimeLeft(timeLeft - 1);\n      }, 1000);\n    } else if (timeLeft === 0) {\n      handleMeditationComplete();\n    }\n\n    return () => clearInterval(interval);\n  }, [isActive, timeLeft, handleMeditationComplete]);\n\n  // Breathing animation cycle\n  useEffect(() => {\n    if (isActive && meditationType === 'breathing') {\n      const cycleBreathing = () => {\n        setBreathPhase('inhale');\n        setTimeout(() => setBreathPhase('hold'), breathingCycle.inhale);\n        setTimeout(() => setBreathPhase('exhale'), breathingCycle.inhale + breathingCycle.hold);\n        setTimeout(() => setBreathPhase('pause'), breathingCycle.inhale + breathingCycle.hold + breathingCycle.exhale);\n      };\n\n      cycleBreathing();\n      const breathInterval = setInterval(cycleBreathing, Object.values(breathingCycle).reduce((a, b) => a + b, 0));\n      \n      return () => clearInterval(breathInterval);\n    }\n  }, [isActive, meditationType, breathingCycle]);\n\n  const startMeditation = () => {\n    setTimeLeft(selectedDuration * 60);\n    setIsActive(true);\n    \n    // Play audio when meditation starts if sound is enabled\n    if (soundEnabled && audioRef.current) {\n      audioRef.current.play()\n        .then(() => console.log('Audio playing successfully'))\n        .catch(e => {\n          console.error('Audio play failed:', e);\n          // Try with the fallback audio element\n          const fallbackAudio = document.querySelector('audio');\n          if (fallbackAudio) {\n            fallbackAudio.play()\n              .then(() => console.log('Fallback audio playing'))\n              .catch(err => console.error('Fallback audio failed too:', err));\n          }\n        });\n    }\n  };\n\n  const pauseMeditation = () => {\n    setIsActive(!isActive);\n    \n    // Pause or play audio based on meditation state\n    if (isActive && audioRef.current) {\n      audioRef.current.pause();\n    } else if (soundEnabled && audioRef.current) {\n      audioRef.current.play().catch(e => console.log('Audio play failed:', e));\n    }\n  };\n\n  const resetMeditation = () => {\n    setIsActive(false);\n    setTimeLeft(selectedDuration * 60);\n    setBreathPhase('inhale');\n    \n    // Stop audio when meditation is reset\n    if (audioRef.current) {\n      audioRef.current.pause();\n      audioRef.current.currentTime = 0;\n    }\n  };\n\n  // ...existing code...\n  // (Removed duplicate handleMeditationComplete declaration)\n  // ...existing code...\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getBreathingInstruction = () => {\n    switch (breathPhase) {\n      case 'inhale': return 'Breathe In...';\n      case 'hold': return 'Hold...';\n      case 'exhale': return 'Breathe Out...';\n      case 'pause': return 'Pause...';\n      default: return 'Breathe...';\n    }\n  };\n\n  const getChakraColor = (index: number) => {\n    const colors = ['#ff0000', '#ff8c00', '#ffff00', '#00ff00', '#0000ff', '#4b0082', '#9400d3'];\n    return colors[index % colors.length];\n  };\n\n  return (\n    <div className=\"min-h-screen pt-20 p-6\">\n      {/* Hidden audio element as fallback */}\n      <audio\n        src={\n          process.env.NODE_ENV === 'production'\n            ? '/2%20minutes%20of%20relaxing%20music,2%20minute%20meditation%20music,2%20minutes%20meditation,music%202%20minute.mp3'\n            : process.env.PUBLIC_URL + '/2%20minutes%20of%20relaxing%20music,2%20minute%20meditation%20music,2%20minutes%20meditation,music%202%20minute.mp3'\n        }\n        loop\n        preload=\"auto\"\n        style={{ display: 'none' }}\n      />\n      <div className=\"max-w-4xl mx-auto\">\n        {/* Header */}\n        <motion.div\n          className=\"text-center mb-8\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n        >\n          <h1 className=\"text-4xl font-spiritual font-bold text-saffron-700 mb-2\">\n            Meditation Center\n          </h1>\n          <p className=\"text-gray-600\">\n            Find inner peace through ancient practices\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Meditation Setup */}\n          <motion.div\n            className=\"lg:col-span-1 space-y-6\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n          >\n            {/* Meditation Type Selection */}\n            <div className=\"spiritual-card p-6\">\n              <h3 className=\"text-lg font-semibold mb-4 text-saffron-700\">\n                Choose Practice\n              </h3>\n              <div className=\"space-y-3\">\n                {meditationTypes.map((type) => (\n                  <button\n                    key={type.type}\n                    onClick={() => setMeditationType(type.type)}\n                    disabled={isActive}\n                    className={`w-full text-left p-3 rounded-lg border transition-all ${\n                      meditationType === type.type\n                        ? 'border-saffron-300 bg-saffron-50'\n                        : 'border-gray-200 hover:border-saffron-200'\n                    } ${isActive ? 'opacity-50 cursor-not-allowed' : ''}`}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-2xl\">{type.icon}</span>\n                      <div>\n                        <div className=\"font-medium text-gray-800\">{type.title}</div>\n                        <div className=\"text-sm text-gray-600\">{type.description}</div>\n                      </div>\n                    </div>\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Duration Selection */}\n            <div className=\"spiritual-card p-6\">\n              <h3 className=\"text-lg font-semibold mb-4 text-saffron-700\">\n                Duration\n              </h3>\n              <div className=\"grid grid-cols-2 gap-2\">\n                {durations.map((duration) => (\n                  <button\n                    key={duration.minutes}\n                    onClick={() => setSelectedDuration(duration.minutes)}\n                    disabled={isActive}\n                    className={`p-3 rounded-lg border text-center transition-all ${\n                      selectedDuration === duration.minutes\n                        ? 'border-saffron-300 bg-saffron-50 text-saffron-700'\n                        : 'border-gray-200 hover:border-saffron-200'\n                    } ${isActive ? 'opacity-50 cursor-not-allowed' : ''}`}\n                  >\n                    <div className=\"font-medium\">{duration.label}</div>\n                    <div className=\"text-xs text-gray-500\">\n                      Level {duration.difficulty}\n                    </div>\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Controls */}\n            <div className=\"spiritual-card p-6\">\n              <div className=\"flex items-center justify-center space-x-4\">\n                {!isActive ? (\n                  <button\n                    onClick={startMeditation}\n                    className=\"karma-button flex items-center space-x-2\"\n                  >\n                    <Play className=\"w-5 h-5\" />\n                    <span>Begin</span>\n                  </button>\n                ) : (\n                  <button\n                    onClick={pauseMeditation}\n                    className=\"karma-button flex items-center space-x-2\"\n                  >\n                    <Pause className=\"w-5 h-5\" />\n                    <span>Pause</span>\n                  </button>\n                )}\n                \n                <button\n                  onClick={resetMeditation}\n                  className=\"px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2\"\n                >\n                  <RotateCcw className=\"w-4 h-4\" />\n                  <span>Reset</span>\n                </button>\n\n                <button\n                  onClick={() => {\n                    const newSoundState = !soundEnabled;\n                    setSoundEnabled(newSoundState);\n                    \n                    // Handle audio based on new sound state\n                    if (audioRef.current) {\n                      if (newSoundState && isActive) {\n                        audioRef.current.play().catch(e => console.log('Audio play failed:', e));\n                      } else {\n                        audioRef.current.pause();\n                      }\n                    }\n                  }}\n                  className=\"p-2 border border-gray-300 rounded-lg hover:bg-gray-50\"\n                >\n                  {soundEnabled ? <Volume2 className=\"w-4 h-4\" /> : <VolumeX className=\"w-4 h-4\" />}\n                </button>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Meditation Visualization */}\n          <motion.div\n            className=\"lg:col-span-2\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n          >\n            <div className=\"spiritual-card p-8 h-full flex flex-col items-center justify-center min-h-[500px]\">\n              {/* Timer Display */}\n              <motion.div\n                className=\"text-6xl font-mono font-bold text-saffron-700 mb-8\"\n                animate={{ scale: isActive ? [1, 1.05, 1] : 1 }}\n                transition={{ duration: 1, repeat: isActive ? Infinity : 0 }}\n              >\n                {formatTime(timeLeft)}\n              </motion.div>\n\n              {/* Meditation Visualization */}\n              <div className=\"relative w-80 h-80 flex items-center justify-center\">\n                {/* Breathing Circle */}\n                {meditationType === 'breathing' && (\n                  <motion.div\n                    className=\"absolute w-64 h-64 rounded-full border-4 border-saffron-300 flex items-center justify-center\"\n                    animate={{\n                      scale: breathPhase === 'inhale' ? 1.3 : breathPhase === 'exhale' ? 0.7 : 1,\n                      borderColor: breathPhase === 'inhale' ? '#10b981' : breathPhase === 'exhale' ? '#3b82f6' : '#f59e0b'\n                    }}\n                    transition={{ duration: breathPhase === 'inhale' ? 4 : breathPhase === 'exhale' ? 8 : 1 }}\n                  >\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl mb-2\">🌬️</div>\n                      <div className=\"text-lg font-medium text-gray-700\">\n                        {getBreathingInstruction()}\n                      </div>\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Mantra Visualization */}\n                {meditationType === 'mantra' && (\n                  <motion.div\n                    className=\"text-center\"\n                    animate={{ scale: [1, 1.1, 1] }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <motion.div\n                      className=\"text-8xl mb-4\"\n                      animate={{ rotate: 360 }}\n                      transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n                    >\n                      🕉️\n                    </motion.div>\n                    <div className=\"sanskrit-text text-2xl text-saffron-700\">\n                      ॐ मणि पद्मे हूँ\n                    </div>\n                    <div className=\"text-sm text-gray-600 mt-2\">\n                      Om Mani Padme Hum\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Chakra Visualization */}\n                {meditationType === 'visualization' && (\n                  <div className=\"relative\">\n                    {[...Array(7)].map((_, i) => (\n                      <motion.div\n                        key={i}\n                        className=\"absolute w-12 h-12 rounded-full\"\n                        style={{\n                          backgroundColor: getChakraColor(i),\n                          top: `${i * 40}px`,\n                          left: '50%',\n                          transform: 'translateX(-50%)'\n                        }}\n                        animate={{\n                          scale: [1, 1.2, 1],\n                          opacity: [0.7, 1, 0.7]\n                        }}\n                        transition={{\n                          duration: 2,\n                          repeat: Infinity,\n                          delay: i * 0.3\n                        }}\n                      />\n                    ))}\n                    <div className=\"text-center mt-80\">\n                      <div className=\"text-lg font-medium text-gray-700\">\n                        Visualize energy flowing through chakras\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Mindfulness Visualization */}\n                {meditationType === 'mindfulness' && (\n                  <motion.div\n                    className=\"text-center\"\n                    animate={{ opacity: [0.5, 1, 0.5] }}\n                    transition={{ duration: 4, repeat: Infinity }}\n                  >\n                    <div className=\"text-6xl mb-4\">🧘‍♂️</div>\n                    <div className=\"text-lg font-medium text-gray-700 mb-2\">\n                      Be present in this moment\n                    </div>\n                    <div className=\"text-sm text-gray-600\">\n                      Observe thoughts without judgment\n                    </div>\n                  </motion.div>\n                )}\n\n                {/* Progress Ring */}\n                <svg className=\"absolute inset-0 w-full h-full -rotate-90\">\n                  <circle\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    r=\"150\"\n                    fill=\"none\"\n                    stroke=\"#f3f4f6\"\n                    strokeWidth=\"4\"\n                  />\n                  <motion.circle\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    r=\"150\"\n                    fill=\"none\"\n                    stroke=\"#f59e0b\"\n                    strokeWidth=\"4\"\n                    strokeLinecap=\"round\"\n                    strokeDasharray={`${2 * Math.PI * 150}`}\n                    strokeDashoffset={`${2 * Math.PI * 150 * (timeLeft / (selectedDuration * 60))}`}\n                    transition={{ duration: 1 }}\n                  />\n                </svg>\n              </div>\n\n              {/* Meditation Benefits */}\n              {!isActive && (\n                <motion.div\n                  className=\"mt-8 text-center\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: 0.5 }}\n                >\n                  <div className=\"text-sm text-gray-600 mb-2\">Benefits of this practice:</div>\n                  <div className=\"flex justify-center space-x-4\">\n                    {Object.entries(meditationTypes.find(t => t.type === meditationType)?.benefits || {}).map(([benefit, value]) => (\n                      <div key={benefit} className=\"text-center\">\n                        <div className=\"text-lg font-bold text-saffron-600\">+{value}</div>\n                        <div className=\"text-xs text-gray-500 capitalize\">{benefit}</div>\n                      </div>\n                    ))}\n                  </div>\n                </motion.div>\n              )}\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MeditationCenter;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,YAAY,QAAQ,uBAAuB;AAGpD,SAASC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,OAAO,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExE,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACvC,MAAM;IAAEC,MAAM;IAAEC,kBAAkB;IAAEC,WAAW;IAAEC;EAAgB,CAAC,GAAGd,YAAY,CAAC,CAAC;EACnF,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAA2D,WAAW,CAAC;EAC3H,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAyC,QAAQ,CAAC;EAChG,MAAM+B,QAAQ,GAAG7B,MAAM,CAA0B,IAAI,CAAC;EAEtD,MAAM8B,eAAe,GAAGjC,KAAK,CAACkC,OAAO,CAAC,MAAO,CAC3C;IACEC,IAAI,EAAE,WAAoB;IAC1BC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,2CAA2C;IACxDC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,QAAiB;IACvBC,KAAK,EAAE,aAAa;IACpBC,WAAW,EAAE,+CAA+C;IAC5DC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAG;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,eAAwB;IAC9BC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE,sCAAsC;IACnDC,IAAI,EAAE,KAAK;IACXC,QAAQ,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAE;EAC9C,CAAC,EACD;IACEP,IAAI,EAAE,aAAsB;IAC5BC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE,qCAAqC;IAClDC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG;EAC9C,CAAC,CACD,EAAE,EAAE,CAAC;EAEP,MAAMC,SAAS,GAAG3C,KAAK,CAACkC,OAAO,CAAC,MAAO,CACrC;IAAEU,OAAO,EAAE,CAAC;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAE,CAAC,EAC7C;IAAEF,OAAO,EAAE,CAAC;IAAEC,KAAK,EAAE,OAAO;IAAEC,UAAU,EAAE;EAAE,CAAC,EAC7C;IAAEF,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAE,CAAC,EAC/C;IAAEF,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAE,CAAC,EAC/C;IAAEF,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAE,CAAC,EAC/C;IAAEF,OAAO,EAAE,EAAE;IAAEC,KAAK,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAE,CAAC,CAC/C,EAAE,EAAE,CAAC;;EAEP;EACA,MAAMC,cAAc,GAAG/C,KAAK,CAACkC,OAAO,CAAC,OAAO;IAC1Cc,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE;EACT,CAAC,CAAC,EAAE,EAAE,CAAC;;EAEP;EACAjD,SAAS,CAAC,MAAM;IACd;IACA,MAAMkD,SAAS,GACbC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GACjC,sHAAsH,GACtHF,OAAO,CAACC,GAAG,CAACE,UAAU,GAAG,sHAAsH;IACrJxB,QAAQ,CAACyB,OAAO,GAAG,IAAIC,KAAK,CAACN,SAAS,CAAC;IACvCpB,QAAQ,CAACyB,OAAO,CAACE,IAAI,GAAG,IAAI;IAC5B3B,QAAQ,CAACyB,OAAO,CAACG,MAAM,GAAG,GAAG;IAE7B,OAAO,MAAM;MACX,IAAI5B,QAAQ,CAACyB,OAAO,EAAE;QACpBzB,QAAQ,CAACyB,OAAO,CAACN,KAAK,CAAC,CAAC;QACxBnB,QAAQ,CAACyB,OAAO,GAAG,IAAI;MACzB;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,wBAAwB,GAAG7D,KAAK,CAAC8D,WAAW,CAAC,MAAM;IACvD,IAAI,CAAC9C,MAAM,EAAE;IAEb,MAAM+C,YAAY,GAAG9B,eAAe,CAAC+B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9B,IAAI,KAAKT,cAAc,CAAE;IAC1E,MAAMwC,QAAQ,GAAGvB,SAAS,CAACqB,IAAI,CAACG,CAAC,IAAIA,CAAC,CAACvB,OAAO,KAAKpB,gBAAgB,CAAE;IAErE,MAAM4C,OAA0B,GAAG;MACjCC,EAAE,EAAE,cAAcC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MAC9BpC,IAAI,EAAET,cAAc;MACpBwC,QAAQ,EAAE1C,gBAAgB;MAC1BsB,UAAU,EAAEoB,QAAQ,CAACpB,UAAU;MAC/B0B,OAAO,EAAET,YAAY,CAACxB;IACxB,CAAC;IAEDtB,kBAAkB,CAACmD,OAAO,CAAC;IAC3BlD,WAAW,CAAC;MAAEsB,MAAM,EAAEuB,YAAY,CAACxB,QAAQ,CAACC;IAAO,CAAC,CAAC;IACrDrB,eAAe,CAAC,0BAA0B4C,YAAY,CAACxB,QAAQ,CAACC,MAAM,gBAAgB,CAAC;IAEvFnB,WAAW,CAAC,KAAK,CAAC;IAClBE,WAAW,CAACC,gBAAgB,GAAG,EAAE,CAAC;;IAElC;IACA,IAAIQ,QAAQ,CAACyB,OAAO,EAAE;MACpBzB,QAAQ,CAACyB,OAAO,CAACN,KAAK,CAAC,CAAC;MACxBnB,QAAQ,CAACyB,OAAO,CAACgB,WAAW,GAAG,CAAC;IAClC;EACF,CAAC,EAAE,CAACzD,MAAM,EAAEiB,eAAe,EAAEP,cAAc,EAAEiB,SAAS,EAAEnB,gBAAgB,EAAEP,kBAAkB,EAAEC,WAAW,EAAEC,eAAe,CAAC,CAAC;EAE5HjB,SAAS,CAAC,MAAM;IACd,IAAIwE,QAAwB;IAE5B,IAAItD,QAAQ,IAAIE,QAAQ,GAAG,CAAC,EAAE;MAC5BoD,QAAQ,GAAGC,WAAW,CAAC,MAAM;QAC3BpD,WAAW,CAACD,QAAQ,GAAG,CAAC,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM,IAAIA,QAAQ,KAAK,CAAC,EAAE;MACzBuC,wBAAwB,CAAC,CAAC;IAC5B;IAEA,OAAO,MAAMe,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACtD,QAAQ,EAAEE,QAAQ,EAAEuC,wBAAwB,CAAC,CAAC;;EAElD;EACA3D,SAAS,CAAC,MAAM;IACd,IAAIkB,QAAQ,IAAIM,cAAc,KAAK,WAAW,EAAE;MAC9C,MAAMmD,cAAc,GAAGA,CAAA,KAAM;QAC3B9C,cAAc,CAAC,QAAQ,CAAC;QACxB+C,UAAU,CAAC,MAAM/C,cAAc,CAAC,MAAM,CAAC,EAAEgB,cAAc,CAACC,MAAM,CAAC;QAC/D8B,UAAU,CAAC,MAAM/C,cAAc,CAAC,QAAQ,CAAC,EAAEgB,cAAc,CAACC,MAAM,GAAGD,cAAc,CAACE,IAAI,CAAC;QACvF6B,UAAU,CAAC,MAAM/C,cAAc,CAAC,OAAO,CAAC,EAAEgB,cAAc,CAACC,MAAM,GAAGD,cAAc,CAACE,IAAI,GAAGF,cAAc,CAACG,MAAM,CAAC;MAChH,CAAC;MAED2B,cAAc,CAAC,CAAC;MAChB,MAAME,cAAc,GAAGJ,WAAW,CAACE,cAAc,EAAEG,MAAM,CAACC,MAAM,CAAClC,cAAc,CAAC,CAACmC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,CAAC;MAE5G,OAAO,MAAMR,aAAa,CAACG,cAAc,CAAC;IAC5C;EACF,CAAC,EAAE,CAAC3D,QAAQ,EAAEM,cAAc,EAAEqB,cAAc,CAAC,CAAC;EAE9C,MAAMsC,eAAe,GAAGA,CAAA,KAAM;IAC5B9D,WAAW,CAACC,gBAAgB,GAAG,EAAE,CAAC;IAClCH,WAAW,CAAC,IAAI,CAAC;;IAEjB;IACA,IAAIO,YAAY,IAAII,QAAQ,CAACyB,OAAO,EAAE;MACpCzB,QAAQ,CAACyB,OAAO,CAAC6B,IAAI,CAAC,CAAC,CACpBC,IAAI,CAAC,MAAMC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC,CAAC,CACrDC,KAAK,CAACC,CAAC,IAAI;QACVH,OAAO,CAACI,KAAK,CAAC,oBAAoB,EAAED,CAAC,CAAC;QACtC;QACA,MAAME,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;QACrD,IAAIF,aAAa,EAAE;UACjBA,aAAa,CAACP,IAAI,CAAC,CAAC,CACjBC,IAAI,CAAC,MAAMC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC,CAAC,CACjDC,KAAK,CAACM,GAAG,IAAIR,OAAO,CAACI,KAAK,CAAC,4BAA4B,EAAEI,GAAG,CAAC,CAAC;QACnE;MACF,CAAC,CAAC;IACN;EACF,CAAC;EAED,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B5E,WAAW,CAAC,CAACD,QAAQ,CAAC;;IAEtB;IACA,IAAIA,QAAQ,IAAIY,QAAQ,CAACyB,OAAO,EAAE;MAChCzB,QAAQ,CAACyB,OAAO,CAACN,KAAK,CAAC,CAAC;IAC1B,CAAC,MAAM,IAAIvB,YAAY,IAAII,QAAQ,CAACyB,OAAO,EAAE;MAC3CzB,QAAQ,CAACyB,OAAO,CAAC6B,IAAI,CAAC,CAAC,CAACI,KAAK,CAACC,CAAC,IAAIH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,CAAC,CAAC,CAAC;IAC1E;EACF,CAAC;EAED,MAAMO,eAAe,GAAGA,CAAA,KAAM;IAC5B7E,WAAW,CAAC,KAAK,CAAC;IAClBE,WAAW,CAACC,gBAAgB,GAAG,EAAE,CAAC;IAClCO,cAAc,CAAC,QAAQ,CAAC;;IAExB;IACA,IAAIC,QAAQ,CAACyB,OAAO,EAAE;MACpBzB,QAAQ,CAACyB,OAAO,CAACN,KAAK,CAAC,CAAC;MACxBnB,QAAQ,CAACyB,OAAO,CAACgB,WAAW,GAAG,CAAC;IAClC;EACF,CAAC;;EAED;EACA;EACA;;EAEA,MAAM0B,UAAU,GAAIC,OAAe,IAAK;IACtC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;EAED,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,QAAQ7E,WAAW;MACjB,KAAK,QAAQ;QAAE,OAAO,eAAe;MACrC,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,gBAAgB;MACtC,KAAK,OAAO;QAAE,OAAO,UAAU;MAC/B;QAAS,OAAO,YAAY;IAC9B;EACF,CAAC;EAED,MAAM8E,cAAc,GAAIC,KAAa,IAAK;IACxC,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;IAC5F,OAAOA,MAAM,CAACD,KAAK,GAAGC,MAAM,CAACC,MAAM,CAAC;EACtC,CAAC;EAED,oBACEnG,OAAA;IAAKoG,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBAErCrG,OAAA;MACEsG,GAAG,EACD7D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GACjC,sHAAsH,GACtHF,OAAO,CAACC,GAAG,CAACE,UAAU,GAAG,sHAC9B;MACDG,IAAI;MACJwD,OAAO,EAAC,MAAM;MACdC,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eACF7G,OAAA;MAAKoG,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAEhCrG,OAAA,CAACR,MAAM,CAACsH,GAAG;QACTV,SAAS,EAAC,kBAAkB;QAC5BW,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAZ,QAAA,gBAE9BrG,OAAA;UAAIoG,SAAS,EAAC,yDAAyD;UAAAC,QAAA,EAAC;QAExE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL7G,OAAA;UAAGoG,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEb7G,OAAA;QAAKoG,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDrG,OAAA,CAACR,MAAM,CAACsH,GAAG;UACTV,SAAS,EAAC,yBAAyB;UACnCW,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEG,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCD,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEG,CAAC,EAAE;UAAE,CAAE;UAAAd,QAAA,gBAG9BrG,OAAA;YAAKoG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCrG,OAAA;cAAIoG,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE5D;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7G,OAAA;cAAKoG,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBhF,eAAe,CAAC+F,GAAG,CAAE7F,IAAI,iBACxBvB,OAAA;gBAEEqH,OAAO,EAAEA,CAAA,KAAMtG,iBAAiB,CAACQ,IAAI,CAACA,IAAI,CAAE;gBAC5C+F,QAAQ,EAAE9G,QAAS;gBACnB4F,SAAS,EAAE,yDACTtF,cAAc,KAAKS,IAAI,CAACA,IAAI,GACxB,kCAAkC,GAClC,0CAA0C,IAC5Cf,QAAQ,GAAG,+BAA+B,GAAG,EAAE,EAAG;gBAAA6F,QAAA,eAEtDrG,OAAA;kBAAKoG,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CrG,OAAA;oBAAMoG,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAE9E,IAAI,CAACG;kBAAI;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7C7G,OAAA;oBAAAqG,QAAA,gBACErG,OAAA;sBAAKoG,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAE9E,IAAI,CAACC;oBAAK;sBAAAkF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC7D7G,OAAA;sBAAKoG,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAE9E,IAAI,CAACE;oBAAW;sBAAAiF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GAfDtF,IAAI,CAACA,IAAI;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBR,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7G,OAAA;YAAKoG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjCrG,OAAA;cAAIoG,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE5D;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7G,OAAA;cAAKoG,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpCtE,SAAS,CAACqF,GAAG,CAAE9D,QAAQ,iBACtBtD,OAAA;gBAEEqH,OAAO,EAAEA,CAAA,KAAMxG,mBAAmB,CAACyC,QAAQ,CAACtB,OAAO,CAAE;gBACrDsF,QAAQ,EAAE9G,QAAS;gBACnB4F,SAAS,EAAE,oDACTxF,gBAAgB,KAAK0C,QAAQ,CAACtB,OAAO,GACjC,mDAAmD,GACnD,0CAA0C,IAC5CxB,QAAQ,GAAG,+BAA+B,GAAG,EAAE,EAAG;gBAAA6F,QAAA,gBAEtDrG,OAAA;kBAAKoG,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAE/C,QAAQ,CAACrB;gBAAK;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnD7G,OAAA;kBAAKoG,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAC,QAC/B,EAAC/C,QAAQ,CAACpB,UAAU;gBAAA;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA,GAZDvD,QAAQ,CAACtB,OAAO;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaf,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7G,OAAA;YAAKoG,SAAS,EAAC,oBAAoB;YAAAC,QAAA,eACjCrG,OAAA;cAAKoG,SAAS,EAAC,4CAA4C;cAAAC,QAAA,GACxD,CAAC7F,QAAQ,gBACRR,OAAA;gBACEqH,OAAO,EAAE5C,eAAgB;gBACzB2B,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBAEpDrG,OAAA,CAACN,IAAI;kBAAC0G,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5B7G,OAAA;kBAAAqG,QAAA,EAAM;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,gBAET7G,OAAA;gBACEqH,OAAO,EAAEhC,eAAgB;gBACzBe,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,gBAEpDrG,OAAA,CAACL,KAAK;kBAACyG,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7B7G,OAAA;kBAAAqG,QAAA,EAAM;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CACT,eAED7G,OAAA;gBACEqH,OAAO,EAAE/B,eAAgB;gBACzBc,SAAS,EAAC,0FAA0F;gBAAAC,QAAA,gBAEpGrG,OAAA,CAACJ,SAAS;kBAACwG,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjC7G,OAAA;kBAAAqG,QAAA,EAAM;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAET7G,OAAA;gBACEqH,OAAO,EAAEA,CAAA,KAAM;kBACb,MAAME,aAAa,GAAG,CAACvG,YAAY;kBACnCC,eAAe,CAACsG,aAAa,CAAC;;kBAE9B;kBACA,IAAInG,QAAQ,CAACyB,OAAO,EAAE;oBACpB,IAAI0E,aAAa,IAAI/G,QAAQ,EAAE;sBAC7BY,QAAQ,CAACyB,OAAO,CAAC6B,IAAI,CAAC,CAAC,CAACI,KAAK,CAACC,CAAC,IAAIH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,CAAC,CAAC,CAAC;oBAC1E,CAAC,MAAM;sBACL3D,QAAQ,CAACyB,OAAO,CAACN,KAAK,CAAC,CAAC;oBAC1B;kBACF;gBACF,CAAE;gBACF6D,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,EAEjErF,YAAY,gBAAGhB,OAAA,CAACH,OAAO;kBAACuG,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG7G,OAAA,CAACF,OAAO;kBAACsG,SAAS,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGb7G,OAAA,CAACR,MAAM,CAACsH,GAAG;UACTV,SAAS,EAAC,eAAe;UACzBW,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEQ,KAAK,EAAE;UAAI,CAAE;UACpCN,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEQ,KAAK,EAAE;UAAE,CAAE;UAAAnB,QAAA,eAElCrG,OAAA;YAAKoG,SAAS,EAAC,mFAAmF;YAAAC,QAAA,gBAEhGrG,OAAA,CAACR,MAAM,CAACsH,GAAG;cACTV,SAAS,EAAC,oDAAoD;cAC9Dc,OAAO,EAAE;gBAAEM,KAAK,EAAEhH,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG;cAAE,CAAE;cAChDiH,UAAU,EAAE;gBAAEnE,QAAQ,EAAE,CAAC;gBAAEoE,MAAM,EAAElH,QAAQ,GAAGmH,QAAQ,GAAG;cAAE,CAAE;cAAAtB,QAAA,EAE5Dd,UAAU,CAAC7E,QAAQ;YAAC;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC,eAGb7G,OAAA;cAAKoG,SAAS,EAAC,qDAAqD;cAAAC,QAAA,GAEjEvF,cAAc,KAAK,WAAW,iBAC7Bd,OAAA,CAACR,MAAM,CAACsH,GAAG;gBACTV,SAAS,EAAC,8FAA8F;gBACxGc,OAAO,EAAE;kBACPM,KAAK,EAAEtG,WAAW,KAAK,QAAQ,GAAG,GAAG,GAAGA,WAAW,KAAK,QAAQ,GAAG,GAAG,GAAG,CAAC;kBAC1E0G,WAAW,EAAE1G,WAAW,KAAK,QAAQ,GAAG,SAAS,GAAGA,WAAW,KAAK,QAAQ,GAAG,SAAS,GAAG;gBAC7F,CAAE;gBACFuG,UAAU,EAAE;kBAAEnE,QAAQ,EAAEpC,WAAW,KAAK,QAAQ,GAAG,CAAC,GAAGA,WAAW,KAAK,QAAQ,GAAG,CAAC,GAAG;gBAAE,CAAE;gBAAAmF,QAAA,eAE1FrG,OAAA;kBAAKoG,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAC1BrG,OAAA;oBAAKoG,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAG;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxC7G,OAAA;oBAAKoG,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAC/CN,uBAAuB,CAAC;kBAAC;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb,EAGA/F,cAAc,KAAK,QAAQ,iBAC1Bd,OAAA,CAACR,MAAM,CAACsH,GAAG;gBACTV,SAAS,EAAC,aAAa;gBACvBc,OAAO,EAAE;kBAAEM,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;gBAAE,CAAE;gBAChCC,UAAU,EAAE;kBAAEnE,QAAQ,EAAE,CAAC;kBAAEoE,MAAM,EAAEC;gBAAS,CAAE;gBAAAtB,QAAA,gBAE9CrG,OAAA,CAACR,MAAM,CAACsH,GAAG;kBACTV,SAAS,EAAC,eAAe;kBACzBc,OAAO,EAAE;oBAAEW,MAAM,EAAE;kBAAI,CAAE;kBACzBJ,UAAU,EAAE;oBAAEnE,QAAQ,EAAE,EAAE;oBAAEoE,MAAM,EAAEC,QAAQ;oBAAEG,IAAI,EAAE;kBAAS,CAAE;kBAAAzB,QAAA,EAChE;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb7G,OAAA;kBAAKoG,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAEzD;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7G,OAAA;kBAAKoG,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE5C;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb,EAGA/F,cAAc,KAAK,eAAe,iBACjCd,OAAA;gBAAKoG,SAAS,EAAC,UAAU;gBAAAC,QAAA,GACtB,CAAC,GAAG0B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACX,GAAG,CAAC,CAACY,CAAC,EAAEC,CAAC,kBACtBjI,OAAA,CAACR,MAAM,CAACsH,GAAG;kBAETV,SAAS,EAAC,iCAAiC;kBAC3CI,KAAK,EAAE;oBACL0B,eAAe,EAAElC,cAAc,CAACiC,CAAC,CAAC;oBAClCE,GAAG,EAAE,GAAGF,CAAC,GAAG,EAAE,IAAI;oBAClBG,IAAI,EAAE,KAAK;oBACXC,SAAS,EAAE;kBACb,CAAE;kBACFnB,OAAO,EAAE;oBACPM,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;oBAClBR,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;kBACvB,CAAE;kBACFS,UAAU,EAAE;oBACVnE,QAAQ,EAAE,CAAC;oBACXoE,MAAM,EAAEC,QAAQ;oBAChBW,KAAK,EAAEL,CAAC,GAAG;kBACb;gBAAE,GAhBGA,CAAC;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiBP,CACF,CAAC,eACF7G,OAAA;kBAAKoG,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,eAChCrG,OAAA;oBAAKoG,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAEnD;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGA/F,cAAc,KAAK,aAAa,iBAC/Bd,OAAA,CAACR,MAAM,CAACsH,GAAG;gBACTV,SAAS,EAAC,aAAa;gBACvBc,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;gBAAE,CAAE;gBACpCS,UAAU,EAAE;kBAAEnE,QAAQ,EAAE,CAAC;kBAAEoE,MAAM,EAAEC;gBAAS,CAAE;gBAAAtB,QAAA,gBAE9CrG,OAAA;kBAAKoG,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAK;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1C7G,OAAA;kBAAKoG,SAAS,EAAC,wCAAwC;kBAAAC,QAAA,EAAC;gBAExD;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN7G,OAAA;kBAAKoG,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAEvC;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CACb,eAGD7G,OAAA;gBAAKoG,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,gBACxDrG,OAAA;kBACEuI,EAAE,EAAC,KAAK;kBACRC,EAAE,EAAC,KAAK;kBACRC,CAAC,EAAC,KAAK;kBACPC,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC;gBAAG;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACF7G,OAAA,CAACR,MAAM,CAACqJ,MAAM;kBACZN,EAAE,EAAC,KAAK;kBACRC,EAAE,EAAC,KAAK;kBACRC,CAAC,EAAC,KAAK;kBACPC,IAAI,EAAC,MAAM;kBACXC,MAAM,EAAC,SAAS;kBAChBC,WAAW,EAAC,GAAG;kBACfE,aAAa,EAAC,OAAO;kBACrBC,eAAe,EAAE,GAAG,CAAC,GAAGrD,IAAI,CAACsD,EAAE,GAAG,GAAG,EAAG;kBACxCC,gBAAgB,EAAE,GAAG,CAAC,GAAGvD,IAAI,CAACsD,EAAE,GAAG,GAAG,IAAItI,QAAQ,IAAIE,gBAAgB,GAAG,EAAE,CAAC,CAAC,EAAG;kBAChF6G,UAAU,EAAE;oBAAEnE,QAAQ,EAAE;kBAAE;gBAAE;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGL,CAACrG,QAAQ,iBACRR,OAAA,CAACR,MAAM,CAACsH,GAAG;cACTV,SAAS,EAAC,kBAAkB;cAC5BW,OAAO,EAAE;gBAAEC,OAAO,EAAE;cAAE,CAAE;cACxBE,OAAO,EAAE;gBAAEF,OAAO,EAAE;cAAE,CAAE;cACxBS,UAAU,EAAE;gBAAEa,KAAK,EAAE;cAAI,CAAE;cAAAjC,QAAA,gBAE3BrG,OAAA;gBAAKoG,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAA0B;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5E7G,OAAA;gBAAKoG,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAC3CjC,MAAM,CAAC8E,OAAO,CAAC,EAAA/I,qBAAA,GAAAkB,eAAe,CAAC+B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC9B,IAAI,KAAKT,cAAc,CAAC,cAAAX,qBAAA,uBAApDA,qBAAA,CAAsDwB,QAAQ,KAAI,CAAC,CAAC,CAAC,CAACyF,GAAG,CAAC,CAAC,CAAC+B,OAAO,EAAEC,KAAK,CAAC,kBACzGpJ,OAAA;kBAAmBoG,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBACxCrG,OAAA;oBAAKoG,SAAS,EAAC,oCAAoC;oBAAAC,QAAA,GAAC,GAAC,EAAC+C,KAAK;kBAAA;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClE7G,OAAA;oBAAKoG,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,EAAE8C;kBAAO;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GAFzDsC,OAAO;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGZ,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3G,EAAA,CA1fID,gBAA0B;EAAA,QACuCR,YAAY;AAAA;AAAA4J,EAAA,GAD7EpJ,gBAA0B;AA4fhC,eAAeA,gBAAgB;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}