# KarmaVerse Environment Variables

# AI Service Configuration (Optional - app works without these)
# Get free API keys from:
# - OpenRouter: https://openrouter.ai/
# - Hugging Face: https://huggingface.co/
# - Groq: https://groq.com/

# Choose one of the following AI services:
REACT_APP_AI_API_KEY=your_api_key_here
REACT_APP_AI_SERVICE=openrouter

# Available AI services: openrouter, huggingface, groq
# If no API key is provided, the app will use local wisdom responses

# Example configurations:
# REACT_APP_AI_API_KEY=sk-or-v1-your-openrouter-key
# REACT_APP_AI_SERVICE=openrouter

# REACT_APP_AI_API_KEY=hf_your-huggingface-token
# REACT_APP_AI_SERVICE=huggingface

# REACT_APP_AI_API_KEY=gsk_your-groq-key
# REACT_APP_AI_SERVICE=groq