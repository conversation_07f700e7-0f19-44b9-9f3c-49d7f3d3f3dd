{"ast": null, "code": "class AudioService {\n  constructor() {\n    this.audioElements = new Map();\n    this.currentTrack = null;\n    this.isEnabled = true;\n    this.masterVolume = 0.7;\n    // Audio track definitions\n    this.tracks = [{\n      id: 'meditation_relaxing',\n      name: 'Relaxing Meditation Music',\n      path: '/2%20minutes%20of%20relaxing%20music,2%20minute%20meditation%20music,2%20minutes%20meditation,music%202%20minute.mp3',\n      loop: true,\n      volume: 0.5,\n      category: 'meditation'\n    }, {\n      id: 'meditation_om',\n      name: 'O<PERSON> Chanting',\n      path: '/One%20minute%20Om%20Chanting.mp3',\n      loop: true,\n      volume: 0.6,\n      category: 'meditation'\n    }, {\n      id: 'yuga_om',\n      name: 'Sacred Om for Yugas',\n      path: '/One%20minute%20Om%20Chanting.mp3',\n      loop: true,\n      volume: 0.4,\n      category: 'yuga'\n    }, {\n      id: 'yuga_kali',\n      name: 'Kali Yuga Theme',\n      path: '/<PERSON>%20Zimmer%20&%20James%20Newton%20Howard%20-%20Why%20So%20Serious_%20(Official%20Audio).mp3',\n      loop: true,\n      volume: 0.3,\n      category: 'yuga'\n    }, {\n      id: 'relaxing_ambient',\n      name: 'Relaxing Ambient',\n      path: '/relaxing-music-2min.mp3',\n      loop: true,\n      volume: 0.3,\n      category: 'ambient'\n    }];\n    this.initializeAudio();\n  }\n  initializeAudio() {\n    // Initialize all audio elements\n    this.tracks.forEach(track => {\n      const audio = new Audio();\n      audio.src = track.path;\n      audio.loop = track.loop;\n      audio.volume = track.volume * this.masterVolume;\n      audio.preload = 'auto';\n\n      // Handle audio loading errors\n      audio.addEventListener('error', e => {\n        console.warn(`Failed to load audio track: ${track.name}`, e);\n      });\n      audio.addEventListener('canplaythrough', () => {\n        console.log(`Audio track loaded: ${track.name}`);\n      });\n      this.audioElements.set(track.id, audio);\n    });\n  }\n\n  // Enable/disable audio globally\n  setEnabled(enabled) {\n    this.isEnabled = enabled;\n    if (!enabled) {\n      this.stopAll();\n    }\n  }\n\n  // Set master volume (0-1)\n  setMasterVolume(volume) {\n    this.masterVolume = Math.max(0, Math.min(1, volume));\n    this.audioElements.forEach((audio, trackId) => {\n      const track = this.tracks.find(t => t.id === trackId);\n      if (track) {\n        audio.volume = track.volume * this.masterVolume;\n      }\n    });\n  }\n\n  // Play a specific track\n  async playTrack(trackId) {\n    if (!this.isEnabled) return false;\n    const audio = this.audioElements.get(trackId);\n    if (!audio) {\n      console.warn(`Audio track not found: ${trackId}`);\n      return false;\n    }\n    try {\n      // Stop current track if different\n      if (this.currentTrack && this.currentTrack !== trackId) {\n        await this.stopTrack(this.currentTrack);\n      }\n      audio.currentTime = 0;\n      await audio.play();\n      this.currentTrack = trackId;\n      console.log(`Playing audio track: ${trackId}`);\n      return true;\n    } catch (error) {\n      console.error(`Failed to play audio track: ${trackId}`, error);\n      return false;\n    }\n  }\n\n  // Stop a specific track\n  async stopTrack(trackId) {\n    const audio = this.audioElements.get(trackId);\n    if (audio) {\n      audio.pause();\n      audio.currentTime = 0;\n      if (this.currentTrack === trackId) {\n        this.currentTrack = null;\n      }\n    }\n  }\n\n  // Stop all audio\n  stopAll() {\n    this.audioElements.forEach(audio => {\n      audio.pause();\n      audio.currentTime = 0;\n    });\n    this.currentTrack = null;\n  }\n\n  // Pause current track\n  pauseCurrent() {\n    if (this.currentTrack) {\n      const audio = this.audioElements.get(this.currentTrack);\n      if (audio) {\n        audio.pause();\n      }\n    }\n  }\n\n  // Resume current track\n  async resumeCurrent() {\n    if (this.currentTrack && this.isEnabled) {\n      const audio = this.audioElements.get(this.currentTrack);\n      if (audio) {\n        try {\n          await audio.play();\n          return true;\n        } catch (error) {\n          console.error('Failed to resume audio', error);\n        }\n      }\n    }\n    return false;\n  }\n\n  // Get appropriate music for meditation\n  getMeditationTrack() {\n    return 'meditation_relaxing';\n  }\n\n  // Get appropriate music for yuga\n  getYugaTrack(yuga) {\n    if (yuga === 'kali') {\n      return 'yuga_kali';\n    }\n    return 'yuga_om'; // For satya, treta, dvapara\n  }\n\n  // Check if audio is currently playing\n  isPlaying() {\n    if (!this.currentTrack) return false;\n    const audio = this.audioElements.get(this.currentTrack);\n    return audio ? !audio.paused : false;\n  }\n\n  // Get current track info\n  getCurrentTrack() {\n    if (!this.currentTrack) return null;\n    return this.tracks.find(t => t.id === this.currentTrack) || null;\n  }\n\n  // Get all available tracks by category\n  getTracksByCategory(category) {\n    return this.tracks.filter(t => t.category === category);\n  }\n\n  // Fade out current track and fade in new track\n  async crossfade(newTrackId, duration = 2000) {\n    if (!this.isEnabled) return false;\n    const newAudio = this.audioElements.get(newTrackId);\n    if (!newAudio) return false;\n    const oldAudio = this.currentTrack ? this.audioElements.get(this.currentTrack) : null;\n\n    // Start new track at volume 0\n    const newTrack = this.tracks.find(t => t.id === newTrackId);\n    if (!newTrack) return false;\n    newAudio.volume = 0;\n    try {\n      await newAudio.play();\n      this.currentTrack = newTrackId;\n\n      // Crossfade\n      const steps = 50;\n      const stepDuration = duration / steps;\n      const oldVolume = oldAudio ? oldAudio.volume : 0;\n      const newVolume = newTrack.volume * this.masterVolume;\n      for (let i = 0; i <= steps; i++) {\n        const progress = i / steps;\n        if (oldAudio) {\n          oldAudio.volume = oldVolume * (1 - progress);\n        }\n        newAudio.volume = newVolume * progress;\n        await new Promise(resolve => setTimeout(resolve, stepDuration));\n      }\n\n      // Stop old audio\n      if (oldAudio) {\n        oldAudio.pause();\n        oldAudio.currentTime = 0;\n      }\n      return true;\n    } catch (error) {\n      console.error(`Failed to crossfade to track: ${newTrackId}`, error);\n      return false;\n    }\n  }\n\n  // Play music based on current yuga with smooth transition\n  async playYugaMusic(yuga) {\n    const trackId = this.getYugaTrack(yuga);\n\n    // If same track is already playing, do nothing\n    if (this.currentTrack === trackId && this.isPlaying()) {\n      return true;\n    }\n\n    // Use crossfade for smooth transition\n    return await this.crossfade(trackId, 3000);\n  }\n\n  // Play meditation music\n  async playMeditationMusic() {\n    const trackId = this.getMeditationTrack();\n    return await this.playTrack(trackId);\n  }\n\n  // Initialize audio with user interaction (required for autoplay policies)\n  async initializeWithUserInteraction() {\n    // Try to play and immediately pause each track to initialize\n    for (const [trackId, audio] of this.audioElements) {\n      try {\n        await audio.play();\n        audio.pause();\n        audio.currentTime = 0;\n      } catch (error) {\n        console.log(`Could not initialize audio track: ${trackId}`);\n      }\n    }\n  }\n}\n\n// Create singleton instance\nexport const audioService = new AudioService();", "map": {"version": 3, "names": ["AudioService", "constructor", "audioElements", "Map", "currentTrack", "isEnabled", "masterVolume", "tracks", "id", "name", "path", "loop", "volume", "category", "initializeAudio", "for<PERSON>ach", "track", "audio", "Audio", "src", "preload", "addEventListener", "e", "console", "warn", "log", "set", "setEnabled", "enabled", "stopAll", "setMasterVolume", "Math", "max", "min", "trackId", "find", "t", "playTrack", "get", "stopTrack", "currentTime", "play", "error", "pause", "pauseCurrent", "resumeCurrent", "getMeditationTrack", "getYugaTrack", "yuga", "isPlaying", "paused", "getCurrentTrack", "getTracksByCategory", "filter", "crossfade", "newTrackId", "duration", "newAudio", "oldAudio", "newTrack", "steps", "stepDuration", "oldVolume", "newVolume", "i", "progress", "Promise", "resolve", "setTimeout", "playYugaMusic", "playMeditationMusic", "initializeWithUserInteraction", "audioService"], "sources": ["D:/Projects/Master1/karmaverse/src/services/audioService.ts"], "sourcesContent": ["import { Yuga } from '../types';\n\nexport interface AudioTrack {\n  id: string;\n  name: string;\n  path: string;\n  loop: boolean;\n  volume: number;\n  category: 'meditation' | 'yuga' | 'ambient';\n}\n\nclass AudioService {\n  private audioElements: Map<string, HTMLAudioElement> = new Map();\n  private currentTrack: string | null = null;\n  private isEnabled: boolean = true;\n  private masterVolume: number = 0.7;\n\n  // Audio track definitions\n  private tracks: AudioTrack[] = [\n    {\n      id: 'meditation_relaxing',\n      name: 'Relaxing Meditation Music',\n      path: '/2%20minutes%20of%20relaxing%20music,2%20minute%20meditation%20music,2%20minutes%20meditation,music%202%20minute.mp3',\n      loop: true,\n      volume: 0.5,\n      category: 'meditation'\n    },\n    {\n      id: 'meditation_om',\n      name: 'Om Chanting',\n      path: '/One%20minute%20Om%20Chanting.mp3',\n      loop: true,\n      volume: 0.6,\n      category: 'meditation'\n    },\n    {\n      id: 'yuga_om',\n      name: 'Sacred Om for Yugas',\n      path: '/One%20minute%20Om%20Chanting.mp3',\n      loop: true,\n      volume: 0.4,\n      category: 'yuga'\n    },\n    {\n      id: 'yuga_kali',\n      name: 'Kali Yuga Theme',\n      path: '/Hans%20Zimmer%20&%20James%20Newton%20Howard%20-%20Why%20So%20Serious_%20(Official%20Audio).mp3',\n      loop: true,\n      volume: 0.3,\n      category: 'yuga'\n    },\n    {\n      id: 'relaxing_ambient',\n      name: 'Relaxing Ambient',\n      path: '/relaxing-music-2min.mp3',\n      loop: true,\n      volume: 0.3,\n      category: 'ambient'\n    }\n  ];\n\n  constructor() {\n    this.initializeAudio();\n  }\n\n  private initializeAudio() {\n    // Initialize all audio elements\n    this.tracks.forEach(track => {\n      const audio = new Audio();\n      audio.src = track.path;\n      audio.loop = track.loop;\n      audio.volume = track.volume * this.masterVolume;\n      audio.preload = 'auto';\n      \n      // Handle audio loading errors\n      audio.addEventListener('error', (e) => {\n        console.warn(`Failed to load audio track: ${track.name}`, e);\n      });\n\n      audio.addEventListener('canplaythrough', () => {\n        console.log(`Audio track loaded: ${track.name}`);\n      });\n\n      this.audioElements.set(track.id, audio);\n    });\n  }\n\n  // Enable/disable audio globally\n  setEnabled(enabled: boolean) {\n    this.isEnabled = enabled;\n    if (!enabled) {\n      this.stopAll();\n    }\n  }\n\n  // Set master volume (0-1)\n  setMasterVolume(volume: number) {\n    this.masterVolume = Math.max(0, Math.min(1, volume));\n    this.audioElements.forEach((audio, trackId) => {\n      const track = this.tracks.find(t => t.id === trackId);\n      if (track) {\n        audio.volume = track.volume * this.masterVolume;\n      }\n    });\n  }\n\n  // Play a specific track\n  async playTrack(trackId: string): Promise<boolean> {\n    if (!this.isEnabled) return false;\n\n    const audio = this.audioElements.get(trackId);\n    if (!audio) {\n      console.warn(`Audio track not found: ${trackId}`);\n      return false;\n    }\n\n    try {\n      // Stop current track if different\n      if (this.currentTrack && this.currentTrack !== trackId) {\n        await this.stopTrack(this.currentTrack);\n      }\n\n      audio.currentTime = 0;\n      await audio.play();\n      this.currentTrack = trackId;\n      console.log(`Playing audio track: ${trackId}`);\n      return true;\n    } catch (error) {\n      console.error(`Failed to play audio track: ${trackId}`, error);\n      return false;\n    }\n  }\n\n  // Stop a specific track\n  async stopTrack(trackId: string): Promise<void> {\n    const audio = this.audioElements.get(trackId);\n    if (audio) {\n      audio.pause();\n      audio.currentTime = 0;\n      if (this.currentTrack === trackId) {\n        this.currentTrack = null;\n      }\n    }\n  }\n\n  // Stop all audio\n  stopAll(): void {\n    this.audioElements.forEach(audio => {\n      audio.pause();\n      audio.currentTime = 0;\n    });\n    this.currentTrack = null;\n  }\n\n  // Pause current track\n  pauseCurrent(): void {\n    if (this.currentTrack) {\n      const audio = this.audioElements.get(this.currentTrack);\n      if (audio) {\n        audio.pause();\n      }\n    }\n  }\n\n  // Resume current track\n  async resumeCurrent(): Promise<boolean> {\n    if (this.currentTrack && this.isEnabled) {\n      const audio = this.audioElements.get(this.currentTrack);\n      if (audio) {\n        try {\n          await audio.play();\n          return true;\n        } catch (error) {\n          console.error('Failed to resume audio', error);\n        }\n      }\n    }\n    return false;\n  }\n\n  // Get appropriate music for meditation\n  getMeditationTrack(): string {\n    return 'meditation_relaxing';\n  }\n\n  // Get appropriate music for yuga\n  getYugaTrack(yuga: Yuga): string {\n    if (yuga === 'kali') {\n      return 'yuga_kali';\n    }\n    return 'yuga_om'; // For satya, treta, dvapara\n  }\n\n  // Check if audio is currently playing\n  isPlaying(): boolean {\n    if (!this.currentTrack) return false;\n    const audio = this.audioElements.get(this.currentTrack);\n    return audio ? !audio.paused : false;\n  }\n\n  // Get current track info\n  getCurrentTrack(): AudioTrack | null {\n    if (!this.currentTrack) return null;\n    return this.tracks.find(t => t.id === this.currentTrack) || null;\n  }\n\n  // Get all available tracks by category\n  getTracksByCategory(category: AudioTrack['category']): AudioTrack[] {\n    return this.tracks.filter(t => t.category === category);\n  }\n\n  // Fade out current track and fade in new track\n  async crossfade(newTrackId: string, duration: number = 2000): Promise<boolean> {\n    if (!this.isEnabled) return false;\n\n    const newAudio = this.audioElements.get(newTrackId);\n    if (!newAudio) return false;\n\n    const oldAudio = this.currentTrack ? this.audioElements.get(this.currentTrack) : null;\n\n    // Start new track at volume 0\n    const newTrack = this.tracks.find(t => t.id === newTrackId);\n    if (!newTrack) return false;\n\n    newAudio.volume = 0;\n\n    try {\n      await newAudio.play();\n      this.currentTrack = newTrackId;\n\n      // Crossfade\n      const steps = 50;\n      const stepDuration = duration / steps;\n      const oldVolume = oldAudio ? oldAudio.volume : 0;\n      const newVolume = newTrack.volume * this.masterVolume;\n\n      for (let i = 0; i <= steps; i++) {\n        const progress = i / steps;\n\n        if (oldAudio) {\n          oldAudio.volume = oldVolume * (1 - progress);\n        }\n        newAudio.volume = newVolume * progress;\n\n        await new Promise(resolve => setTimeout(resolve, stepDuration));\n      }\n\n      // Stop old audio\n      if (oldAudio) {\n        oldAudio.pause();\n        oldAudio.currentTime = 0;\n      }\n\n      return true;\n    } catch (error) {\n      console.error(`Failed to crossfade to track: ${newTrackId}`, error);\n      return false;\n    }\n  }\n\n  // Play music based on current yuga with smooth transition\n  async playYugaMusic(yuga: Yuga): Promise<boolean> {\n    const trackId = this.getYugaTrack(yuga);\n\n    // If same track is already playing, do nothing\n    if (this.currentTrack === trackId && this.isPlaying()) {\n      return true;\n    }\n\n    // Use crossfade for smooth transition\n    return await this.crossfade(trackId, 3000);\n  }\n\n  // Play meditation music\n  async playMeditationMusic(): Promise<boolean> {\n    const trackId = this.getMeditationTrack();\n    return await this.playTrack(trackId);\n  }\n\n  // Initialize audio with user interaction (required for autoplay policies)\n  async initializeWithUserInteraction(): Promise<void> {\n    // Try to play and immediately pause each track to initialize\n    for (const [trackId, audio] of this.audioElements) {\n      try {\n        await audio.play();\n        audio.pause();\n        audio.currentTime = 0;\n      } catch (error) {\n        console.log(`Could not initialize audio track: ${trackId}`);\n      }\n    }\n  }\n}\n\n// Create singleton instance\nexport const audioService = new AudioService();\n"], "mappings": "AAWA,MAAMA,YAAY,CAAC;EAkDjBC,WAAWA,CAAA,EAAG;IAAA,KAjDNC,aAAa,GAAkC,IAAIC,GAAG,CAAC,CAAC;IAAA,KACxDC,YAAY,GAAkB,IAAI;IAAA,KAClCC,SAAS,GAAY,IAAI;IAAA,KACzBC,YAAY,GAAW,GAAG;IAElC;IAAA,KACQC,MAAM,GAAiB,CAC7B;MACEC,EAAE,EAAE,qBAAqB;MACzBC,IAAI,EAAE,2BAA2B;MACjCC,IAAI,EAAE,sHAAsH;MAC5HC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,GAAG;MACXC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,eAAe;MACnBC,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE,mCAAmC;MACzCC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,GAAG;MACXC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,SAAS;MACbC,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE,mCAAmC;MACzCC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,GAAG;MACXC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,WAAW;MACfC,IAAI,EAAE,iBAAiB;MACvBC,IAAI,EAAE,iGAAiG;MACvGC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,GAAG;MACXC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,kBAAkB;MACtBC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,0BAA0B;MAChCC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,GAAG;MACXC,QAAQ,EAAE;IACZ,CAAC,CACF;IAGC,IAAI,CAACC,eAAe,CAAC,CAAC;EACxB;EAEQA,eAAeA,CAAA,EAAG;IACxB;IACA,IAAI,CAACP,MAAM,CAACQ,OAAO,CAACC,KAAK,IAAI;MAC3B,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;MACzBD,KAAK,CAACE,GAAG,GAAGH,KAAK,CAACN,IAAI;MACtBO,KAAK,CAACN,IAAI,GAAGK,KAAK,CAACL,IAAI;MACvBM,KAAK,CAACL,MAAM,GAAGI,KAAK,CAACJ,MAAM,GAAG,IAAI,CAACN,YAAY;MAC/CW,KAAK,CAACG,OAAO,GAAG,MAAM;;MAEtB;MACAH,KAAK,CAACI,gBAAgB,CAAC,OAAO,EAAGC,CAAC,IAAK;QACrCC,OAAO,CAACC,IAAI,CAAC,+BAA+BR,KAAK,CAACP,IAAI,EAAE,EAAEa,CAAC,CAAC;MAC9D,CAAC,CAAC;MAEFL,KAAK,CAACI,gBAAgB,CAAC,gBAAgB,EAAE,MAAM;QAC7CE,OAAO,CAACE,GAAG,CAAC,uBAAuBT,KAAK,CAACP,IAAI,EAAE,CAAC;MAClD,CAAC,CAAC;MAEF,IAAI,CAACP,aAAa,CAACwB,GAAG,CAACV,KAAK,CAACR,EAAE,EAAES,KAAK,CAAC;IACzC,CAAC,CAAC;EACJ;;EAEA;EACAU,UAAUA,CAACC,OAAgB,EAAE;IAC3B,IAAI,CAACvB,SAAS,GAAGuB,OAAO;IACxB,IAAI,CAACA,OAAO,EAAE;MACZ,IAAI,CAACC,OAAO,CAAC,CAAC;IAChB;EACF;;EAEA;EACAC,eAAeA,CAAClB,MAAc,EAAE;IAC9B,IAAI,CAACN,YAAY,GAAGyB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAErB,MAAM,CAAC,CAAC;IACpD,IAAI,CAACV,aAAa,CAACa,OAAO,CAAC,CAACE,KAAK,EAAEiB,OAAO,KAAK;MAC7C,MAAMlB,KAAK,GAAG,IAAI,CAACT,MAAM,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,EAAE,KAAK0B,OAAO,CAAC;MACrD,IAAIlB,KAAK,EAAE;QACTC,KAAK,CAACL,MAAM,GAAGI,KAAK,CAACJ,MAAM,GAAG,IAAI,CAACN,YAAY;MACjD;IACF,CAAC,CAAC;EACJ;;EAEA;EACA,MAAM+B,SAASA,CAACH,OAAe,EAAoB;IACjD,IAAI,CAAC,IAAI,CAAC7B,SAAS,EAAE,OAAO,KAAK;IAEjC,MAAMY,KAAK,GAAG,IAAI,CAACf,aAAa,CAACoC,GAAG,CAACJ,OAAO,CAAC;IAC7C,IAAI,CAACjB,KAAK,EAAE;MACVM,OAAO,CAACC,IAAI,CAAC,0BAA0BU,OAAO,EAAE,CAAC;MACjD,OAAO,KAAK;IACd;IAEA,IAAI;MACF;MACA,IAAI,IAAI,CAAC9B,YAAY,IAAI,IAAI,CAACA,YAAY,KAAK8B,OAAO,EAAE;QACtD,MAAM,IAAI,CAACK,SAAS,CAAC,IAAI,CAACnC,YAAY,CAAC;MACzC;MAEAa,KAAK,CAACuB,WAAW,GAAG,CAAC;MACrB,MAAMvB,KAAK,CAACwB,IAAI,CAAC,CAAC;MAClB,IAAI,CAACrC,YAAY,GAAG8B,OAAO;MAC3BX,OAAO,CAACE,GAAG,CAAC,wBAAwBS,OAAO,EAAE,CAAC;MAC9C,OAAO,IAAI;IACb,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdnB,OAAO,CAACmB,KAAK,CAAC,+BAA+BR,OAAO,EAAE,EAAEQ,KAAK,CAAC;MAC9D,OAAO,KAAK;IACd;EACF;;EAEA;EACA,MAAMH,SAASA,CAACL,OAAe,EAAiB;IAC9C,MAAMjB,KAAK,GAAG,IAAI,CAACf,aAAa,CAACoC,GAAG,CAACJ,OAAO,CAAC;IAC7C,IAAIjB,KAAK,EAAE;MACTA,KAAK,CAAC0B,KAAK,CAAC,CAAC;MACb1B,KAAK,CAACuB,WAAW,GAAG,CAAC;MACrB,IAAI,IAAI,CAACpC,YAAY,KAAK8B,OAAO,EAAE;QACjC,IAAI,CAAC9B,YAAY,GAAG,IAAI;MAC1B;IACF;EACF;;EAEA;EACAyB,OAAOA,CAAA,EAAS;IACd,IAAI,CAAC3B,aAAa,CAACa,OAAO,CAACE,KAAK,IAAI;MAClCA,KAAK,CAAC0B,KAAK,CAAC,CAAC;MACb1B,KAAK,CAACuB,WAAW,GAAG,CAAC;IACvB,CAAC,CAAC;IACF,IAAI,CAACpC,YAAY,GAAG,IAAI;EAC1B;;EAEA;EACAwC,YAAYA,CAAA,EAAS;IACnB,IAAI,IAAI,CAACxC,YAAY,EAAE;MACrB,MAAMa,KAAK,GAAG,IAAI,CAACf,aAAa,CAACoC,GAAG,CAAC,IAAI,CAAClC,YAAY,CAAC;MACvD,IAAIa,KAAK,EAAE;QACTA,KAAK,CAAC0B,KAAK,CAAC,CAAC;MACf;IACF;EACF;;EAEA;EACA,MAAME,aAAaA,CAAA,EAAqB;IACtC,IAAI,IAAI,CAACzC,YAAY,IAAI,IAAI,CAACC,SAAS,EAAE;MACvC,MAAMY,KAAK,GAAG,IAAI,CAACf,aAAa,CAACoC,GAAG,CAAC,IAAI,CAAClC,YAAY,CAAC;MACvD,IAAIa,KAAK,EAAE;QACT,IAAI;UACF,MAAMA,KAAK,CAACwB,IAAI,CAAC,CAAC;UAClB,OAAO,IAAI;QACb,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdnB,OAAO,CAACmB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF;IACF;IACA,OAAO,KAAK;EACd;;EAEA;EACAI,kBAAkBA,CAAA,EAAW;IAC3B,OAAO,qBAAqB;EAC9B;;EAEA;EACAC,YAAYA,CAACC,IAAU,EAAU;IAC/B,IAAIA,IAAI,KAAK,MAAM,EAAE;MACnB,OAAO,WAAW;IACpB;IACA,OAAO,SAAS,CAAC,CAAC;EACpB;;EAEA;EACAC,SAASA,CAAA,EAAY;IACnB,IAAI,CAAC,IAAI,CAAC7C,YAAY,EAAE,OAAO,KAAK;IACpC,MAAMa,KAAK,GAAG,IAAI,CAACf,aAAa,CAACoC,GAAG,CAAC,IAAI,CAAClC,YAAY,CAAC;IACvD,OAAOa,KAAK,GAAG,CAACA,KAAK,CAACiC,MAAM,GAAG,KAAK;EACtC;;EAEA;EACAC,eAAeA,CAAA,EAAsB;IACnC,IAAI,CAAC,IAAI,CAAC/C,YAAY,EAAE,OAAO,IAAI;IACnC,OAAO,IAAI,CAACG,MAAM,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,EAAE,KAAK,IAAI,CAACJ,YAAY,CAAC,IAAI,IAAI;EAClE;;EAEA;EACAgD,mBAAmBA,CAACvC,QAAgC,EAAgB;IAClE,OAAO,IAAI,CAACN,MAAM,CAAC8C,MAAM,CAACjB,CAAC,IAAIA,CAAC,CAACvB,QAAQ,KAAKA,QAAQ,CAAC;EACzD;;EAEA;EACA,MAAMyC,SAASA,CAACC,UAAkB,EAAEC,QAAgB,GAAG,IAAI,EAAoB;IAC7E,IAAI,CAAC,IAAI,CAACnD,SAAS,EAAE,OAAO,KAAK;IAEjC,MAAMoD,QAAQ,GAAG,IAAI,CAACvD,aAAa,CAACoC,GAAG,CAACiB,UAAU,CAAC;IACnD,IAAI,CAACE,QAAQ,EAAE,OAAO,KAAK;IAE3B,MAAMC,QAAQ,GAAG,IAAI,CAACtD,YAAY,GAAG,IAAI,CAACF,aAAa,CAACoC,GAAG,CAAC,IAAI,CAAClC,YAAY,CAAC,GAAG,IAAI;;IAErF;IACA,MAAMuD,QAAQ,GAAG,IAAI,CAACpD,MAAM,CAAC4B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,EAAE,KAAK+C,UAAU,CAAC;IAC3D,IAAI,CAACI,QAAQ,EAAE,OAAO,KAAK;IAE3BF,QAAQ,CAAC7C,MAAM,GAAG,CAAC;IAEnB,IAAI;MACF,MAAM6C,QAAQ,CAAChB,IAAI,CAAC,CAAC;MACrB,IAAI,CAACrC,YAAY,GAAGmD,UAAU;;MAE9B;MACA,MAAMK,KAAK,GAAG,EAAE;MAChB,MAAMC,YAAY,GAAGL,QAAQ,GAAGI,KAAK;MACrC,MAAME,SAAS,GAAGJ,QAAQ,GAAGA,QAAQ,CAAC9C,MAAM,GAAG,CAAC;MAChD,MAAMmD,SAAS,GAAGJ,QAAQ,CAAC/C,MAAM,GAAG,IAAI,CAACN,YAAY;MAErD,KAAK,IAAI0D,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIJ,KAAK,EAAEI,CAAC,EAAE,EAAE;QAC/B,MAAMC,QAAQ,GAAGD,CAAC,GAAGJ,KAAK;QAE1B,IAAIF,QAAQ,EAAE;UACZA,QAAQ,CAAC9C,MAAM,GAAGkD,SAAS,IAAI,CAAC,GAAGG,QAAQ,CAAC;QAC9C;QACAR,QAAQ,CAAC7C,MAAM,GAAGmD,SAAS,GAAGE,QAAQ;QAEtC,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEN,YAAY,CAAC,CAAC;MACjE;;MAEA;MACA,IAAIH,QAAQ,EAAE;QACZA,QAAQ,CAACf,KAAK,CAAC,CAAC;QAChBe,QAAQ,CAAClB,WAAW,GAAG,CAAC;MAC1B;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdnB,OAAO,CAACmB,KAAK,CAAC,iCAAiCa,UAAU,EAAE,EAAEb,KAAK,CAAC;MACnE,OAAO,KAAK;IACd;EACF;;EAEA;EACA,MAAM2B,aAAaA,CAACrB,IAAU,EAAoB;IAChD,MAAMd,OAAO,GAAG,IAAI,CAACa,YAAY,CAACC,IAAI,CAAC;;IAEvC;IACA,IAAI,IAAI,CAAC5C,YAAY,KAAK8B,OAAO,IAAI,IAAI,CAACe,SAAS,CAAC,CAAC,EAAE;MACrD,OAAO,IAAI;IACb;;IAEA;IACA,OAAO,MAAM,IAAI,CAACK,SAAS,CAACpB,OAAO,EAAE,IAAI,CAAC;EAC5C;;EAEA;EACA,MAAMoC,mBAAmBA,CAAA,EAAqB;IAC5C,MAAMpC,OAAO,GAAG,IAAI,CAACY,kBAAkB,CAAC,CAAC;IACzC,OAAO,MAAM,IAAI,CAACT,SAAS,CAACH,OAAO,CAAC;EACtC;;EAEA;EACA,MAAMqC,6BAA6BA,CAAA,EAAkB;IACnD;IACA,KAAK,MAAM,CAACrC,OAAO,EAAEjB,KAAK,CAAC,IAAI,IAAI,CAACf,aAAa,EAAE;MACjD,IAAI;QACF,MAAMe,KAAK,CAACwB,IAAI,CAAC,CAAC;QAClBxB,KAAK,CAAC0B,KAAK,CAAC,CAAC;QACb1B,KAAK,CAACuB,WAAW,GAAG,CAAC;MACvB,CAAC,CAAC,OAAOE,KAAK,EAAE;QACdnB,OAAO,CAACE,GAAG,CAAC,qCAAqCS,OAAO,EAAE,CAAC;MAC7D;IACF;EACF;AACF;;AAEA;AACA,OAAO,MAAMsC,YAAY,GAAG,IAAIxE,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}