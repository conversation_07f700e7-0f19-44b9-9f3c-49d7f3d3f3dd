[{"d:\\Projects\\Master1\\karmaverse\\src\\index.tsx": "1", "d:\\Projects\\Master1\\karmaverse\\src\\reportWebVitals.ts": "2", "d:\\Projects\\Master1\\karmaverse\\src\\App.tsx": "3", "d:\\Projects\\Master1\\karmaverse\\src\\store\\gameStore.ts": "4", "d:\\Projects\\Master1\\karmaverse\\src\\components\\Dashboard\\Dashboard.tsx": "5", "d:\\Projects\\Master1\\karmaverse\\src\\components\\Avatar\\AvatarCreation.tsx": "6", "d:\\Projects\\Master1\\karmaverse\\src\\components\\UI\\Navigation.tsx": "7", "d:\\Projects\\Master1\\karmaverse\\src\\components\\Avatar\\AvatarDisplay.tsx": "8", "d:\\Projects\\Master1\\karmaverse\\src\\data\\scriptures.ts": "9", "d:\\Projects\\Master1\\karmaverse\\src\\data\\dilemmas.ts": "10", "d:\\Projects\\Master1\\karmaverse\\src\\components\\Meditation\\MeditationCenter.tsx": "11", "d:\\Projects\\Master1\\karmaverse\\src\\components\\Journal\\SpiritualJournal.tsx": "12", "d:\\Projects\\Master1\\karmaverse\\src\\components\\World\\WorldExplorer.tsx": "13", "d:\\Projects\\Master1\\karmaverse\\src\\components\\Quests\\QuestSystem.tsx": "14", "d:\\Projects\\Master1\\karmaverse\\src\\components\\AI\\SpiritualGuide.tsx": "15", "d:\\Projects\\Master1\\karmaverse\\src\\hooks\\useAI.ts": "16", "d:\\Projects\\Master1\\karmaverse\\src\\services\\aiService.ts": "17", "D:\\Projects\\Master1\\karmaverse\\src\\index.tsx": "18", "D:\\Projects\\Master1\\karmaverse\\src\\reportWebVitals.ts": "19", "D:\\Projects\\Master1\\karmaverse\\src\\App.tsx": "20", "D:\\Projects\\Master1\\karmaverse\\src\\store\\gameStore.ts": "21", "D:\\Projects\\Master1\\karmaverse\\src\\components\\Dashboard\\Dashboard.tsx": "22", "D:\\Projects\\Master1\\karmaverse\\src\\components\\Journal\\SpiritualJournal.tsx": "23", "D:\\Projects\\Master1\\karmaverse\\src\\components\\Avatar\\AvatarCreation.tsx": "24", "D:\\Projects\\Master1\\karmaverse\\src\\components\\World\\WorldExplorer.tsx": "25", "D:\\Projects\\Master1\\karmaverse\\src\\components\\Meditation\\MeditationCenter.tsx": "26", "D:\\Projects\\Master1\\karmaverse\\src\\components\\AI\\SpiritualGuide.tsx": "27", "D:\\Projects\\Master1\\karmaverse\\src\\components\\Quests\\QuestSystem.tsx": "28", "D:\\Projects\\Master1\\karmaverse\\src\\components\\UI\\Navigation.tsx": "29", "D:\\Projects\\Master1\\karmaverse\\src\\components\\Avatar\\AvatarDisplay.tsx": "30", "D:\\Projects\\Master1\\karmaverse\\src\\data\\dilemmas.ts": "31", "D:\\Projects\\Master1\\karmaverse\\src\\data\\scriptures.ts": "32", "D:\\Projects\\Master1\\karmaverse\\src\\hooks\\useAI.ts": "33", "D:\\Projects\\Master1\\karmaverse\\src\\services\\aiService.ts": "34", "D:\\Projects\\Master1\\karmaverse\\src\\components\\World\\TempleList.tsx": "35", "D:\\Projects\\Master1\\karmaverse\\src\\components\\World\\YugaDetails.tsx": "36", "D:\\Projects\\Master1\\karmaverse\\src\\components\\World\\EnchantingAnimation.tsx": "37", "D:\\Projects\\Master1\\karmaverse\\src\\services\\audioService.ts": "38"}, {"size": 554, "mtime": 1751265172768, "results": "39", "hashOfConfig": "40"}, {"size": 425, "mtime": 1751265171918, "results": "41", "hashOfConfig": "40"}, {"size": 4877, "mtime": 1751268059210, "results": "42", "hashOfConfig": "40"}, {"size": 10583, "mtime": 1751269026402, "results": "43", "hashOfConfig": "40"}, {"size": 14750, "mtime": 1751266281514, "results": "44", "hashOfConfig": "40"}, {"size": 9066, "mtime": 1751266376245, "results": "45", "hashOfConfig": "40"}, {"size": 5544, "mtime": 1751266426300, "results": "46", "hashOfConfig": "40"}, {"size": 7272, "mtime": 1751268686162, "results": "47", "hashOfConfig": "40"}, {"size": 9117, "mtime": 1751265838134, "results": "48", "hashOfConfig": "40"}, {"size": 12117, "mtime": 1751265919691, "results": "49", "hashOfConfig": "40"}, {"size": 16614, "mtime": 1751266966724, "results": "50", "hashOfConfig": "40"}, {"size": 20115, "mtime": 1751267064384, "results": "51", "hashOfConfig": "40"}, {"size": 20892, "mtime": 1751267197313, "results": "52", "hashOfConfig": "40"}, {"size": 19590, "mtime": 1751267571497, "results": "53", "hashOfConfig": "40"}, {"size": 8108, "mtime": 1751268034332, "results": "54", "hashOfConfig": "40"}, {"size": 1472, "mtime": 1751267656727, "results": "55", "hashOfConfig": "40"}, {"size": 11763, "mtime": 1751268770710, "results": "56", "hashOfConfig": "40"}, {"size": 554, "mtime": 1751265172768, "results": "57", "hashOfConfig": "58"}, {"size": 425, "mtime": 1751265171918, "results": "59", "hashOfConfig": "58"}, {"size": 4865, "mtime": 1751271904901, "results": "60", "hashOfConfig": "58"}, {"size": 10564, "mtime": 1751281074500, "results": "61", "hashOfConfig": "58"}, {"size": 14753, "mtime": 1751284657833, "results": "62", "hashOfConfig": "58"}, {"size": 20082, "mtime": 1751284675983, "results": "63", "hashOfConfig": "58"}, {"size": 9066, "mtime": 1751266376245, "results": "64", "hashOfConfig": "58"}, {"size": 27475, "mtime": 1751298488323, "results": "65", "hashOfConfig": "58"}, {"size": 18486, "mtime": 1751298336815, "results": "66", "hashOfConfig": "58"}, {"size": 8102, "mtime": 1751284639854, "results": "67", "hashOfConfig": "58"}, {"size": 19591, "mtime": 1751285877446, "results": "68", "hashOfConfig": "58"}, {"size": 5544, "mtime": 1751266426300, "results": "69", "hashOfConfig": "58"}, {"size": 7272, "mtime": 1751268686162, "results": "70", "hashOfConfig": "58"}, {"size": 12117, "mtime": 1751265919691, "results": "71", "hashOfConfig": "58"}, {"size": 9117, "mtime": 1751265838134, "results": "72", "hashOfConfig": "58"}, {"size": 1472, "mtime": 1751267656727, "results": "73", "hashOfConfig": "58"}, {"size": 11763, "mtime": 1751268770710, "results": "74", "hashOfConfig": "58"}, {"size": 9837, "mtime": 1751273966453, "results": "75", "hashOfConfig": "58"}, {"size": 10855, "mtime": 1751272744357, "results": "76", "hashOfConfig": "58"}, {"size": 2748, "mtime": 1751273326449, "results": "77", "hashOfConfig": "58"}, {"size": 7864, "mtime": 1751297400102, "results": "78", "hashOfConfig": "58"}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10quk2n", {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "rils<PERSON>s", {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "d:\\Projects\\Master1\\karmaverse\\src\\index.tsx", [], [], "d:\\Projects\\Master1\\karmaverse\\src\\reportWebVitals.ts", [], [], "d:\\Projects\\Master1\\karmaverse\\src\\App.tsx", [], [], "d:\\Projects\\Master1\\karmaverse\\src\\store\\gameStore.ts", ["193"], [], "d:\\Projects\\Master1\\karmaverse\\src\\components\\Dashboard\\Dashboard.tsx", ["194"], [], "d:\\Projects\\Master1\\karmaverse\\src\\components\\Avatar\\AvatarCreation.tsx", [], [], "d:\\Projects\\Master1\\karmaverse\\src\\components\\UI\\Navigation.tsx", [], [], "d:\\Projects\\Master1\\karmaverse\\src\\components\\Avatar\\AvatarDisplay.tsx", [], [], "d:\\Projects\\Master1\\karmaverse\\src\\data\\scriptures.ts", [], [], "d:\\Projects\\Master1\\karmaverse\\src\\data\\dilemmas.ts", [], [], "d:\\Projects\\Master1\\karmaverse\\src\\components\\Meditation\\MeditationCenter.tsx", ["195", "196", "197"], [], "d:\\Projects\\Master1\\karmaverse\\src\\components\\Journal\\SpiritualJournal.tsx", ["198", "199", "200", "201", "202"], [], "d:\\Projects\\Master1\\karmaverse\\src\\components\\World\\WorldExplorer.tsx", ["203", "204", "205", "206", "207", "208", "209", "210", "211"], [], "d:\\Projects\\Master1\\karmaverse\\src\\components\\Quests\\QuestSystem.tsx", ["212", "213", "214", "215", "216", "217"], [], "d:\\Projects\\Master1\\karmaverse\\src\\components\\AI\\SpiritualGuide.tsx", ["218"], [], "d:\\Projects\\Master1\\karmaverse\\src\\hooks\\useAI.ts", [], [], "d:\\Projects\\Master1\\karmaverse\\src\\services\\aiService.ts", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\index.tsx", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\reportWebVitals.ts", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\App.tsx", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\store\\gameStore.ts", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\components\\Dashboard\\Dashboard.tsx", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\components\\Journal\\SpiritualJournal.tsx", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\components\\Avatar\\AvatarCreation.tsx", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\components\\World\\WorldExplorer.tsx", ["219"], [], "D:\\Projects\\Master1\\karmaverse\\src\\components\\Meditation\\MeditationCenter.tsx", ["220"], [], "D:\\Projects\\Master1\\karmaverse\\src\\components\\AI\\SpiritualGuide.tsx", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\components\\Quests\\QuestSystem.tsx", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\components\\UI\\Navigation.tsx", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\components\\Avatar\\AvatarDisplay.tsx", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\data\\dilemmas.ts", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\data\\scriptures.ts", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\hooks\\useAI.ts", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\services\\aiService.ts", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\components\\World\\TempleList.tsx", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\components\\World\\YugaDetails.tsx", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\components\\World\\EnchantingAnimation.tsx", [], [], "D:\\Projects\\Master1\\karmaverse\\src\\services\\audioService.ts", [], [], {"ruleId": "221", "severity": 1, "message": "222", "line": 2, "column": 19, "nodeType": "223", "messageId": "224", "endLine": 2, "endColumn": 36}, {"ruleId": "221", "severity": 1, "message": "225", "line": 18, "column": 5, "nodeType": "223", "messageId": "224", "endLine": 18, "endColumn": 19}, {"ruleId": "221", "severity": 1, "message": "226", "line": 2, "column": 18, "nodeType": "223", "messageId": "224", "endLine": 2, "endColumn": 33}, {"ruleId": "227", "severity": 1, "message": "228", "line": 76, "column": 6, "nodeType": "229", "endLine": 76, "endColumn": 26, "suggestions": "230"}, {"ruleId": "227", "severity": 1, "message": "231", "line": 93, "column": 6, "nodeType": "229", "endLine": 93, "endColumn": 32, "suggestions": "232"}, {"ruleId": "221", "severity": 1, "message": "233", "line": 6, "column": 20, "nodeType": "223", "messageId": "224", "endLine": 6, "endColumn": 25}, {"ruleId": "221", "severity": 1, "message": "234", "line": 6, "column": 27, "nodeType": "223", "messageId": "224", "endLine": 6, "endColumn": 32}, {"ruleId": "221", "severity": 1, "message": "235", "line": 6, "column": 40, "nodeType": "223", "messageId": "224", "endLine": 6, "endColumn": 45}, {"ruleId": "221", "severity": 1, "message": "236", "line": 6, "column": 47, "nodeType": "223", "messageId": "224", "endLine": 6, "endColumn": 52}, {"ruleId": "221", "severity": 1, "message": "237", "line": 6, "column": 54, "nodeType": "223", "messageId": "224", "endLine": 6, "endColumn": 57}, {"ruleId": "221", "severity": 1, "message": "238", "line": 1, "column": 27, "nodeType": "223", "messageId": "224", "endLine": 1, "endColumn": 36}, {"ruleId": "221", "severity": 1, "message": "239", "line": 5, "column": 10, "nodeType": "223", "messageId": "224", "endLine": 5, "endColumn": 15}, {"ruleId": "221", "severity": 1, "message": "240", "line": 5, "column": 22, "nodeType": "223", "messageId": "224", "endLine": 5, "endColumn": 27}, {"ruleId": "221", "severity": 1, "message": "241", "line": 5, "column": 29, "nodeType": "223", "messageId": "224", "endLine": 5, "endColumn": 37}, {"ruleId": "221", "severity": 1, "message": "242", "line": 5, "column": 49, "nodeType": "223", "messageId": "224", "endLine": 5, "endColumn": 54}, {"ruleId": "221", "severity": 1, "message": "243", "line": 5, "column": 56, "nodeType": "223", "messageId": "224", "endLine": 5, "endColumn": 61}, {"ruleId": "221", "severity": 1, "message": "244", "line": 5, "column": 63, "nodeType": "223", "messageId": "224", "endLine": 5, "endColumn": 66}, {"ruleId": "221", "severity": 1, "message": "233", "line": 5, "column": 68, "nodeType": "223", "messageId": "224", "endLine": 5, "endColumn": 73}, {"ruleId": "221", "severity": 1, "message": "245", "line": 5, "column": 75, "nodeType": "223", "messageId": "224", "endLine": 5, "endColumn": 81}, {"ruleId": "221", "severity": 1, "message": "246", "line": 4, "column": 17, "nodeType": "223", "messageId": "224", "endLine": 4, "endColumn": 28}, {"ruleId": "221", "severity": 1, "message": "247", "line": 5, "column": 18, "nodeType": "223", "messageId": "224", "endLine": 5, "endColumn": 22}, {"ruleId": "221", "severity": 1, "message": "248", "line": 5, "column": 24, "nodeType": "223", "messageId": "224", "endLine": 5, "endColumn": 29}, {"ruleId": "221", "severity": 1, "message": "249", "line": 5, "column": 31, "nodeType": "223", "messageId": "224", "endLine": 5, "endColumn": 35}, {"ruleId": "221", "severity": 1, "message": "244", "line": 5, "column": 56, "nodeType": "223", "messageId": "224", "endLine": 5, "endColumn": 59}, {"ruleId": "227", "severity": 1, "message": "250", "line": 123, "column": 6, "nodeType": "229", "endLine": 123, "endColumn": 31, "suggestions": "251"}, {"ruleId": "221", "severity": 1, "message": "252", "line": 5, "column": 36, "nodeType": "223", "messageId": "224", "endLine": 5, "endColumn": 40}, {"ruleId": "221", "severity": 1, "message": "253", "line": 4, "column": 10, "nodeType": "223", "messageId": "224", "endLine": 4, "endColumn": 22}, {"ruleId": "221", "severity": 1, "message": "254", "line": 1, "column": 38, "nodeType": "223", "messageId": "224", "endLine": 1, "endColumn": 44}, "@typescript-eslint/no-unused-vars", "'createJSONStorage' is defined but never used.", "Identifier", "unusedVar", "'currentDilemma' is assigned a value but never used.", "'AnimatePresence' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleMeditationComplete'. Either include it or remove the dependency array.", "ArrayExpression", ["255"], "React Hook useEffect has a missing dependency: 'breathingCycle'. Either include it or remove the dependency array.", ["256"], "'Heart' is defined but never used.", "'Brain' is defined but never used.", "'Smile' is defined but never used.", "'Frown' is defined but never used.", "'Meh' is defined but never used.", "'useEffect' is defined but never used.", "'Globe' is defined but never used.", "'Cloud' is defined but never used.", "'TreePine' is defined but never used.", "'Waves' is defined but never used.", "'Users' is defined but never used.", "'Zap' is defined but never used.", "'Shield' is defined but never used.", "'QuestReward' is defined but never used.", "'Star' is defined but never used.", "'Clock' is defined but never used.", "'Gift' is defined but never used.", "React Hook useEffect has a missing dependency: 'allQuests'. Either include it or remove the dependency array.", ["257"], "'User' is defined but never used.", "'audioService' is defined but never used.", "'useRef' is defined but never used.", {"desc": "258", "fix": "259"}, {"desc": "260", "fix": "261"}, {"desc": "262", "fix": "263"}, "Update the dependencies array to be: [handleMeditationComplete, isActive, timeLeft]", {"range": "264", "text": "265"}, "Update the dependencies array to be: [breathingCycle, isActive, meditationType]", {"range": "266", "text": "267"}, "Update the dependencies array to be: [allQuests, avatar, completedQuests]", {"range": "268", "text": "269"}, [2540, 2560], "[handleMeditationComplete, isActive, timeLeft]", [3248, 3274], "[breathingCycle, isActive, meditationType]", [4253, 4278], "[allQuests, avatar, completedQuests]"]